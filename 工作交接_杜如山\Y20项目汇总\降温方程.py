import numpy as np
import matplotlib.pyplot as plt

# 定义函数
def get_tcool(temp):
    if temp < 1500:
        tcool = -3.34375 + (temp - 1000) * 0.41406 / 100
    elif temp < 1900:
        tcool = -1.6875 + (temp - 1500) * 0.24219 / 100
    elif temp < 2500:
        tcool = -0.71875 + (temp - 1900) * 0.11979 / 100
    elif temp < 3000:
        tcool = (temp - 2500) * 0.15 / 100
    else:
        tcool = 0.75 + (temp - 3000) * 0.14583 / 100
    return 100.0 * tcool

# 生成温度数据
temps = np.arange(0, 3500, 1)  # 从0到3500，步长为1
tcool_values = [get_tcool(temp) for temp in temps]

# 绘制图像
plt.figure(figsize=(10, 6))
plt.plot(temps, tcool_values, label='Tcool')
plt.xlabel('Temperature (°C)')
plt.ylabel('Tcool (°C)')
plt.title('Tcool Function')
plt.grid(True)
plt.legend()
plt.show()