{"cells": [{"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "Optimization results for Instrument 1:\n", "L1: -1.647674,\n", "L2: 1.000000,\n", "L3: -1.000000,\n", "L4: 0.135377,\n", "scale: 38.315109,\n", "bias: 2.275930,\n", "Minimum MSE: 0.171060\n", "\n", "Prediction results comparison:\n", "  区间    G   B   预测R   真实R\n", " 0-2 1298 890 0.959  1.27\n", " 0-2 1271 861 1.522  1.72\n", " 0-2 1246 829 2.174  2.10\n", " 2-4 1238 789 3.061  3.14\n", " 2-4 1211 765 3.594  3.31\n", " 2-4 1230 782 3.214  2.85\n", " 4-6 1117 645 6.667  6.66\n", " 4-6 1135 650 6.550  6.79\n", " 4-6 1118 644 6.698  6.66\n", " 6-8  949 592 7.971  7.75\n", " 6-8  945 562 8.988  7.87\n", " 6-8 1011 588 8.254  8.19\n", "8-10  933 557 9.134  9.99\n", "8-10  905 532 9.979 10.20\n", "8-10  925 546 9.512  9.76\n", "Overall prediction variance: 9.2812\n", "\n", "Variance per range:\n", "0-2 range variance: 0.3697\n", "2-4 range variance: 0.0753\n", "4-6 range variance: 0.0061\n", "8-10 range variance: 0.1792\n", "10-12 range variance: nan\n"]}], "source": ["import math\n", "import pandas as pd\n", "import numpy as np\n", "from scipy.optimize import minimize\n", "\n", "# Data preprocessing function\n", "def prepare_data(data):\n", "    \"\"\"将分区间列重组为长格式\"\"\"\n", "    ranges = ['0-2', '2-4', '4-6', '6-8','8-10','10-12']\n", "    merged = []\n", "    \n", "    for r in ranges:\n", "        # Extract data for each range, ensuring no NaN values in '位置'\n", "        subset = data[data['位置'].str.contains(r, na=False)].copy()\n", "        subset['range'] = r\n", "        merged.append(subset)\n", "    \n", "    return pd.concat(merged).reset_index(drop=True)\n", "\n", "# Function to calculate the optimized Y value\n", "def calculate_Y_optimized(Blue, Dark, Green, ML_COEF):\n", "    \"\"\"使用机器学习优化后的预测函数\"\"\"\n", "    # Fixed parameters\n", "    g0 = 3000\n", "    b0 = 3048\n", "    g1 = 141\n", "    b1 = 165\n", "    d0 = 0\n", "    #cali_raw_para.d1, cali_raw_para.g1, cali_raw_para.b1,cali_raw_para.temph,cali_raw_para.templ\n", "    \n", "    try:\n", "        # Calculate intermediate variables\n", "        L1 = math.log10(Blue - Dark - b1) / math.log10(b0 - d0 - b1) + math.log10((Blue - Dark) / b0)\n", "        L2 = math.log10(Green - Dark - g1) / math.log10(g0 - d0 - g1) + math.log10((Green - Dark) / g0)\n", "        L3 = math.log10((Green - Dark)/(g0 - g1)) - math.log10((Blue - Dark)/(b0 - b1))\n", "        L4 = math.log10((Green - Dark)/(g0 - g1)) / math.log10((Blue - Dark)/(b0 - b1))\n", "        \n", "        # Apply optimized coefficients\n", "        X = (ML_COEF['L1'] * L1 + \n", "             ML_COEF['L2'] * L2 + \n", "             ML_COEF['L3'] * L3 + \n", "             ML_COEF['L4'] * L4)\n", "        \n", "        return ML_COEF['scale'] * X + ML_COEF['bias']\n", "    except:\n", "        return np.nan\n", "\n", "# Objective function for optimization\n", "def objective(params, instrument_data, target_column):\n", "    \"\"\"目标函数：最小化预测值与真实值的MSE\"\"\"\n", "    coef_L1, coef_L2, coef_L3, coef_L4, scale, bias = params\n", "    g0 = 3000\n", "    b0 = 3048\n", "    g1 = 141\n", "    b1 = 165\n", "    d0 = 0\n", "\n", "    predictions = []\n", "    for _, row in instrument_data.iterrows():\n", "        try:\n", "            # Use the raw measurement values from the selected instrument\n", "            Green = row['G']\n", "            Blue = row['B']\n", "            Dark = 2  # Fixed dark current value\n", "            \n", "            # Calculate intermediate variables\n", "            L1 = math.log10(Blue - Dark - b1) / math.log10(b0 - d0 - b1) + math.log10((Blue - Dark) / b0)\n", "            L2 = math.log10(Green - Dark - g1) / math.log10(g0 - d0 - g1) + math.log10((Green - Dark) / g0)\n", "            L3 = math.log10((Green - Dark)/(g0 - g1)) - math.log10((Blue - Dark)/(b0 - b1))\n", "            L4 = math.log10((Green - Dark)/(g0 - g1)) / math.log10((Blue - Dark)/(b0 - b1))\n", "            \n", "            # Use the coefficients to be optimized\n", "            X = coef_L1 * L1 + coef_L2 * L2 + coef_L3 * L3 + coef_L4 * L4\n", "            pred = scale * X + bias\n", "            predictions.append(pred)\n", "        except:\n", "            return np.inf\n", "    \n", "    mse = np.mean((np.array(predictions) - instrument_data[target_column])**2)\n", "    return mse\n", "\n", "# Load data\n", "data = pd.read_excel(\"3.4新探头对比1,3号.xlsx\")\n", "\n", "# Prepare data\n", "prepared_data = prepare_data(data)\n", "\n", "# Function to run optimization for a specific instrument\n", "def optimize_instrument(instrument_number):\n", "    # Select data for the specified instrument\n", "    if instrument_number == 1:\n", "        instrument_data = prepared_data[['range', '仪器1_G', '仪器1_B', '仪器A_R']].rename(columns={'仪器1_G': 'G', '仪器1_B': 'B', '仪器A_R': 'A_R'})\n", "    elif instrument_number == 3:\n", "        instrument_data = prepared_data[['range', '仪器3_G', '仪器3_B', '仪器A_R']].rename(columns={'仪器3_G': 'G', '仪器3_B': 'B', '仪器A_R': 'A_R'})\n", "    else:\n", "        raise ValueError(\"Invalid instrument number. Choose 1 or 3.\")\n", "    \n", "    # Define parameter bounds\n", "    bounds = [\n", "        (-2, 0),    # L1 coefficient\n", "        (-1, 1),    # L2 coefficient\n", "        (-1, 1),    # L3 coefficient\n", "        (-1, 1),    # L4 coefficient\n", "        (30, 45),   # Scale coefficient\n", "        (-10, 15)   # <PERSON>ias term\n", "    ]\n", "    \n", "    # Initial guess values\n", "    initial_guess = [-0.9975, 0.55, -0.202, -0.195, 38.105, 6.467]\n", "    \n", "    # Run optimization\n", "    result = minimize(\n", "        objective,\n", "        initial_guess,\n", "        args=(instrument_data, 'A_R'),\n", "        method='L-BFGS-B',\n", "        bounds=bounds,\n", "        options={'maxiter': 1000, 'disp': True}\n", "    )\n", "    \n", "    # Extract optimized parameters\n", "    ML_COEF = {\n", "        'L1': result.x[0],\n", "        'L2': result.x[1],\n", "        'L3': result.x[2],\n", "        'L4': result.x[3],\n", "        'scale': result.x[4],\n", "        'bias': result.x[5]\n", "    }\n", "    \n", "    # Validate the final results\n", "    predictions = []\n", "    for _, row in instrument_data.iterrows():\n", "        pred = calculate_Y_optimized(row['B'], 2, row['G'], ML_COEF)\n", "        predictions.append(pred)\n", "    \n", "    # Output results\n", "    print(f\"\\nOptimization results for Instrument {instrument_number}:\")\n", "    for name, val in ML_COEF.items():\n", "        print(f\"{name}: {val:.6f},\")\n", "    print(f\"Minimum MSE: {result.fun:.6f}\")\n", "    \n", "    # Compare predictions with true values\n", "    result_df = pd.DataFrame({\n", "        '区间': instrument_data['range'],\n", "        'G': instrument_data['G'],\n", "        'B': instrument_data['B'],\n", "        '预测R': np.round(predictions, 3),\n", "        '真实R': instrument_data['A_R']\n", "    })\n", "    print(\"\\nPrediction results comparison:\")\n", "    print(result_df.to_string(index=False))\n", "    print(f\"Overall prediction variance: {np.var(predictions):.4f}\")\n", "    \n", "    # Calculate variance per range\n", "    print(\"\\nVariance per range:\")\n", "    for r in ['0-2', '2-4', '4-6','8-10','10-12']:\n", "        subset = result_df[result_df['区间'] == r]\n", "        print(f\"{r} range variance: {subset['预测R'].var():.4f}\")\n", "\n", "# Run optimization for Instrument 1\n", "optimize_instrument(1)\n", "\n", "# Run optimization for Instrument 3\n", "# optimize_instrument(3)"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.11"}}, "nbformat": 4, "nbformat_minor": 2}