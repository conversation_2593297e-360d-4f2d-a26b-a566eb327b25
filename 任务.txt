[toc]

### 任务

- LED换位测试
  - 白板测试(3000，3000)、校准测试(校准的值在2600-3200(工厂测试时改为2800-3200))、皮肤测试
- PD(5515,5515C,)和LED(0603,0402)**选型**共八种情况
  - PD：发光二极管返回的波长通过光电二极管获取并发送对应信号
- 算法融合：在同一位置的同型号不同探头的设备测量结果一致

### 代码

- 先校准再修改pwm再读数据：FIXEDCALDATA宏注释
- 不校准，pwm为固定值：定义FIXEDCALDATA
- mb/mg：通过发光二极管返回的波长通过光电二极管发送信号，进而调整pwm以及获取测量值
- py脚本：将mid_data中的数据转换为filtered_data，再将其复制到excel进行分析

### 数据及参数

- 蓝绿灯波峰在450、560，使用在这两处获取数据
- LED：光强、波峰的波长、发光角度、温度影响
- PD：40-50+的主波长、最大接收面积等

### 数据分析

- IQR四分位距