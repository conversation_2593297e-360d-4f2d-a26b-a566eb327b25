{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {"id": "sdwM0i8aS-eY"}, "outputs": [], "source": []}, {"cell_type": "markdown", "metadata": {"id": "iyQ3nTmQTF1e"}, "source": ["# 新段落"]}, {"cell_type": "code", "execution_count": 11, "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 356}, "id": "4wf8DFjKTcg9", "outputId": "5145bd1e-71c7-4d63-a833-5c0def1c1a43"}, "outputs": [{"ename": "AttributeError", "evalue": "partially initialized module 'pandas' has no attribute '_pandas_parser_CAPI' (most likely due to a circular import)", "output_type": "error", "traceback": ["\u001b[31m---------------------------------------------------------------------------\u001b[39m", "\u001b[31mAttributeError\u001b[39m                            <PERSON><PERSON> (most recent call last)", "\u001b[36mCell\u001b[39m\u001b[36m \u001b[39m\u001b[32mIn[11]\u001b[39m\u001b[32m, line 1\u001b[39m\n\u001b[32m----> \u001b[39m\u001b[32m1\u001b[39m \u001b[38;5;28;01mimport\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34;01<PERSON><PERSON><PERSON>\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;28;01mas\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34;01mpd\u001b[39;00m\n\u001b[32m      2\u001b[39m \u001b[38;5;28;01mimport\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34;01mnumpy\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;28;01mas\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34;01mnp\u001b[39;00m\n\u001b[32m      3\u001b[39m \u001b[38;5;28;01mimport\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34;01mmatplotlib\u001b[39;00m\u001b[34;01m.\u001b[39;00m\u001b[34;01mpyplot\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;28;01mas\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34;01mplt\u001b[39;00m\n", "\u001b[36mFile \u001b[39m\u001b[32m~\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python311\\site-packages\\pandas\\__init__.py:151\u001b[39m\n\u001b[32m    132\u001b[39m \u001b[38;5;28;01mfrom\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34;01mpandas\u001b[39;00m\u001b[34;01m.\u001b[39;00m\u001b[34;01mcore\u001b[39;00m\u001b[34;01m.\u001b[39;00m\u001b[34;01mcomputation\u001b[39;00m\u001b[34;01m.\u001b[39;00m\u001b[34;01mapi\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;28;01mimport\u001b[39;00m \u001b[38;5;28meval\u001b[39m\n\u001b[32m    134\u001b[39m \u001b[38;5;28;01mfrom\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34;01mpandas\u001b[39;00m\u001b[34;01m.\u001b[39;00m\u001b[34;01mcore\u001b[39;00m\u001b[34;01m.\u001b[39;00m\u001b[34;01m<PERSON>ape\u001b[39;00m\u001b[34;01m.\u001b[39;00m\u001b[34;01mapi\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;28;01mimport\u001b[39;00m (\n\u001b[32m    135\u001b[39m     concat,\n\u001b[32m    136\u001b[39m     lreshape,\n\u001b[32m   (...)\u001b[39m\u001b[32m    148\u001b[39m     qcut,\n\u001b[32m    149\u001b[39m )\n\u001b[32m--> \u001b[39m\u001b[32m151\u001b[39m \u001b[38;5;28;01mfrom\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34;01mpandas\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;28;01mimport\u001b[39;00m api, arrays, errors, io, plotting, tseries\n\u001b[32m    152\u001b[39m \u001b[38;5;28;01mfrom\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34;01mpandas\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;28;01mimport\u001b[39;00m testing\n\u001b[32m    153\u001b[39m \u001b[38;5;28;01mfrom\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34;01mpandas\u001b[39;00m\u001b[34;01m.\u001b[39;00m\u001b[34;01mutil\u001b[39;00m\u001b[34;01m.\u001b[39;00m\u001b[34;01m_print_versions\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;28;01mimport\u001b[39;00m show_versions\n", "\u001b[36mFile \u001b[39m\u001b[32m~\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python311\\site-packages\\pandas\\api\\__init__.py:2\u001b[39m\n\u001b[32m      1\u001b[39m \u001b[33;03m\"\"\" public toolkit API \"\"\"\u001b[39;00m\n\u001b[32m----> \u001b[39m\u001b[32m2\u001b[39m \u001b[38;5;28;01mfrom\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34;01mpandas\u001b[39;00m\u001b[34;01m.\u001b[39;00m\u001b[34;01mapi\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;28;01mimport\u001b[39;00m (\n\u001b[32m      3\u001b[39m     extensions,\n\u001b[32m      4\u001b[39m     indexers,\n\u001b[32m      5\u001b[39m     interchange,\n\u001b[32m      6\u001b[39m     types,\n\u001b[32m      7\u001b[39m     typing,\n\u001b[32m      8\u001b[39m )\n\u001b[32m     10\u001b[39m __all__ = [\n\u001b[32m     11\u001b[39m     \u001b[33m\"\u001b[39m\u001b[33minterchange\u001b[39m\u001b[33m\"\u001b[39m,\n\u001b[32m     12\u001b[39m     \u001b[33m\"\u001b[39m\u001b[33mextensions\u001b[39m\u001b[33m\"\u001b[39m,\n\u001b[32m   (...)\u001b[39m\u001b[32m     15\u001b[39m     \u001b[33m\"\u001b[39m\u001b[33mtyping\u001b[39m\u001b[33m\"\u001b[39m,\n\u001b[32m     16\u001b[39m ]\n", "\u001b[36mFile \u001b[39m\u001b[32m~\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python311\\site-packages\\pandas\\api\\typing\\__init__.py:31\u001b[39m\n\u001b[32m     19\u001b[39m \u001b[38;5;28;01mfrom\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34;01mpandas\u001b[39;00m\u001b[34;01m.\u001b[39;00m\u001b[34;01mcore\u001b[39;00m\u001b[34;01m.\u001b[39;00m\u001b[34;01mwindow\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;28;01mimport\u001b[39;00m (\n\u001b[32m     20\u001b[39m     Expanding,\n\u001b[32m     21\u001b[39m     ExpandingGroupby,\n\u001b[32m   (...)\u001b[39m\u001b[32m     26\u001b[39m     Window,\n\u001b[32m     27\u001b[39m )\n\u001b[32m     29\u001b[39m \u001b[38;5;66;03m# TODO: Can't import Styler without importing jinja2\u001b[39;00m\n\u001b[32m     30\u001b[39m \u001b[38;5;66;03m# from pandas.io.formats.style import Styler\u001b[39;00m\n\u001b[32m---> \u001b[39m\u001b[32m31\u001b[39m \u001b[38;5;28;01mfrom\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34;01mpandas\u001b[39;00m\u001b[34;01m.\u001b[39;00m\u001b[34;01mio\u001b[39;00m\u001b[34;01m.\u001b[39;00m\u001b[34;01mjson\u001b[39;00m\u001b[34;01m.\u001b[39;00m\u001b[34;01m_json\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;28;01mimport\u001b[39;00m JsonReader\n\u001b[32m     32\u001b[39m \u001b[38;5;28;01mfrom\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34;01mpandas\u001b[39;00m\u001b[34;01m.\u001b[39;00m\u001b[34;01mio\u001b[39;00m\u001b[34;01m.\u001b[39;00m\u001b[34;01mstata\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;28;01mimport\u001b[39;00m StataReader\n\u001b[32m     34\u001b[39m __all__ = [\n\u001b[32m     35\u001b[39m     \u001b[33m\"\u001b[39m\u001b[33mDataFrameGroupBy\u001b[39m\u001b[33m\"\u001b[39m,\n\u001b[32m     36\u001b[39m     \u001b[33m\"\u001b[39m\u001b[33mDatetimeIndexResamplerGroupby\u001b[39m\u001b[33m\"\u001b[39m,\n\u001b[32m   (...)\u001b[39m\u001b[32m     54\u001b[39m     \u001b[33m\"\u001b[39m\u001b[33mWindow\u001b[39m\u001b[33m\"\u001b[39m,\n\u001b[32m     55\u001b[39m ]\n", "\u001b[36mFile \u001b[39m\u001b[32m~\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python311\\site-packages\\pandas\\io\\json\\__init__.py:1\u001b[39m\n\u001b[32m----> \u001b[39m\u001b[32m1\u001b[39m \u001b[38;5;28;01mfrom\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34;01mpandas\u001b[39;00m\u001b[34;01m.\u001b[39;00m\u001b[34;01mio\u001b[39;00m\u001b[34;01m.\u001b[39;00m\u001b[34;01mjson\u001b[39;00m\u001b[34;01m.\u001b[39;00m\u001b[34;01m_json\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;28;01mimport\u001b[39;00m (\n\u001b[32m      2\u001b[39m     read_json,\n\u001b[32m      3\u001b[39m     to_json,\n\u001b[32m      4\u001b[39m     ujson_dumps,\n\u001b[32m      5\u001b[39m     ujson_loads,\n\u001b[32m      6\u001b[39m )\n\u001b[32m      7\u001b[39m \u001b[38;5;28;01mfrom\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34;01mpandas\u001b[39;00m\u001b[34;01m.\u001b[39;00m\u001b[34;01mio\u001b[39;00m\u001b[34;01m.\u001b[39;00m\u001b[34;01mjson\u001b[39;00m\u001b[34;01m.\u001b[39;00m\u001b[34;01m_table_schema\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;28;01mimport\u001b[39;00m build_table_schema\n\u001b[32m      9\u001b[39m __all__ = [\n\u001b[32m     10\u001b[39m     \u001b[33m\"\u001b[39m\u001b[33mujson_dumps\u001b[39m\u001b[33m\"\u001b[39m,\n\u001b[32m     11\u001b[39m     \u001b[33m\"\u001b[39m\u001b[33mujson_loads\u001b[39m\u001b[33m\"\u001b[39m,\n\u001b[32m   (...)\u001b[39m\u001b[32m     14\u001b[39m     \u001b[33m\"\u001b[39m\u001b[33mbuild_table_schema\u001b[39m\u001b[33m\"\u001b[39m,\n\u001b[32m     15\u001b[39m ]\n", "\u001b[36mFile \u001b[39m\u001b[32m~\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python311\\site-packages\\pandas\\io\\json\\_json.py:71\u001b[39m\n\u001b[32m     66\u001b[39m \u001b[38;5;28;01mfrom\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34;01mpandas\u001b[39;00m\u001b[34;01m.\u001b[39;00m\u001b[34;01mio\u001b[39;00m\u001b[34;01m.\u001b[39;00m\u001b[34;01mjson\u001b[39;00m\u001b[34;01m.\u001b[39;00m\u001b[34;01m_normalize\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;28;01mimport\u001b[39;00m convert_to_line_delimits\n\u001b[32m     67\u001b[39m \u001b[38;5;28;01mfrom\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34;01mpandas\u001b[39;00m\u001b[34;01m.\u001b[39;00m\u001b[34;01mio\u001b[39;00m\u001b[34;01m.\u001b[39;00m\u001b[34;01mj<PERSON>\u001b[39;00m\u001b[34;01m.\u001b[39;00m\u001b[34;01m_table_schema\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;28;01mimport\u001b[39;00m (\n\u001b[32m     68\u001b[39m     build_table_schema,\n\u001b[32m     69\u001b[39m     parse_table_schema,\n\u001b[32m     70\u001b[39m )\n\u001b[32m---> \u001b[39m\u001b[32m71\u001b[39m \u001b[38;5;28;01mfrom\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34;01mpandas\u001b[39;00m\u001b[34;01m.\u001b[39;00m\u001b[34;01mio\u001b[39;00m\u001b[34;01m.\u001b[39;00m\u001b[34;01mparsers\u001b[39;00m\u001b[34;01m.\u001b[39;00m\u001b[34;01mreaders\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;28;01mimport\u001b[39;00m validate_integer\n\u001b[32m     73\u001b[39m \u001b[38;5;28;01mif\u001b[39;00m TYPE_CHECKING:\n\u001b[32m     74\u001b[39m     \u001b[38;5;28;01mfrom\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34;01mcollections\u001b[39;00m\u001b[34;01m.\u001b[39;00m\u001b[34;01mabc\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;28;01mimport\u001b[39;00m (\n\u001b[32m     75\u001b[39m         Hashable,\n\u001b[32m     76\u001b[39m         Mapping,\n\u001b[32m     77\u001b[39m     )\n", "\u001b[36mFile \u001b[39m\u001b[32m~\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python311\\site-packages\\pandas\\io\\parsers\\__init__.py:1\u001b[39m\n\u001b[32m----> \u001b[39m\u001b[32m1\u001b[39m \u001b[38;5;28;01mfrom\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34;01mpandas\u001b[39;00m\u001b[34;01m.\u001b[39;00m\u001b[34;01mio\u001b[39;00m\u001b[34;01m.\u001b[39;00m\u001b[34;01mparsers\u001b[39;00m\u001b[34;01m.\u001b[39;00m\u001b[34;01mreaders\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;28;01mimport\u001b[39;00m (\n\u001b[32m      2\u001b[39m     TextFileReader,\n\u001b[32m      3\u001b[39m     TextParser,\n\u001b[32m      4\u001b[39m     read_csv,\n\u001b[32m      5\u001b[39m     read_fwf,\n\u001b[32m      6\u001b[39m     read_table,\n\u001b[32m      7\u001b[39m )\n\u001b[32m      9\u001b[39m __all__ = [\u001b[33m\"\u001b[39m\u001b[33mTextFileReader\u001b[39m\u001b[33m\"\u001b[39m, \u001b[33m\"\u001b[39m\u001b[33mTextParser\u001b[39m\u001b[33m\"\u001b[39m, \u001b[33m\"\u001b[39m\u001b[33mread_csv\u001b[39m\u001b[33m\"\u001b[39m, \u001b[33m\"\u001b[39m\u001b[33mread_fwf\u001b[39m\u001b[33m\"\u001b[39m, \u001b[33m\"\u001b[39m\u001b[33mread_table\u001b[39m\u001b[33m\"\u001b[39m]\n", "\u001b[36mFile \u001b[39m\u001b[32m~\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python311\\site-packages\\pandas\\io\\parsers\\readers.py:32\u001b[39m\n\u001b[32m     29\u001b[39m \u001b[38;5;28;01mfrom\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34;01mpanda<PERSON>\u001b[39;00m\u001b[34;01m.\u001b[39;00m\u001b[34;01m_config\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;28;01mimport\u001b[39;00m using_copy_on_write\n\u001b[32m     31\u001b[39m \u001b[38;5;28;01mfrom\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34;01mpandas\u001b[39;00m\u001b[34;01m.\u001b[39;00m\u001b[34;01m_libs\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;28;01mimport\u001b[39;00m lib\n\u001b[32m---> \u001b[39m\u001b[32m32\u001b[39m \u001b[38;5;28;01mfrom\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34;01mpandas\u001b[39;00m\u001b[34;01m.\u001b[39;00m\u001b[34;01m_libs\u001b[39;00m\u001b[34;01m.\u001b[39;00m\u001b[34;01mparsers\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;28;01mimport\u001b[39;00m STR_NA_VALUES\n\u001b[32m     33\u001b[39m \u001b[38;5;28;01mfrom\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34;01mpandas\u001b[39;00m\u001b[34;01m.\u001b[39;00m\u001b[34;01merrors\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;28;01mimport\u001b[39;00m (\n\u001b[32m     34\u001b[39m     AbstractMethodError,\n\u001b[32m     35\u001b[39m     ParserWarning,\n\u001b[32m     36\u001b[39m )\n\u001b[32m     37\u001b[39m \u001b[38;5;28;01mfrom\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34;01mpandas\u001b[39;00m\u001b[34;01m.\u001b[39;00m\u001b[34;01mutil\u001b[39;00m\u001b[34;01m.\u001b[39;00m\u001b[34;01m_decorators\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;28;01mimport\u001b[39;00m Appender\n", "\u001b[36mFile \u001b[39m\u001b[32mparsers.pyx:1418\u001b[39m, in \u001b[36minit pandas._libs.parsers\u001b[39m\u001b[34m()\u001b[39m\n", "\u001b[31mAttributeError\u001b[39m: partially initialized module 'pandas' has no attribute '_pandas_parser_CAPI' (most likely due to a circular import)"]}], "source": ["import pandas as pd\n", "import numpy as np\n", "import matplotlib.pyplot as plt\n", "# import seaborn as sns\n", "import scipy.stats as stats\n", "from sklearn.linear_model import LinearRegression\n", "import os\n", "\n", "\n", "os.chdir(\"D:\\E3A\\亿杉算法实习\\温度补偿\\医院临床测试记录\")\n", "# 1. 加载数据\n", "file_path = \"4.30临床数据分析.xlsx\"  # 这里替换为你的文件路径\n", "data = pd.read_excel(file_path)\n", "\n", "# 2. 提取相关的列（假设\"YSJ(Old)\"和\"YSJ(New)\"是数据中的列名）\n", "x = data['戴维']  # 自变量\n", "y = data['A']  # 因变量\n", "\n", "# 3. 计算皮尔逊相关系数\n", "correlation, _ = stats.pearsonr(x, y)\n", "print(f\"Pearson correlation coefficient: {correlation}\")\n", "\n", "# 4. 进行线性回归分析\n", "X = x.values.reshape(-1, 1)  # 自变量，需要reshape成二维数组\n", "y = y.values\n", "\n", "model = LinearRegression()\n", "model.fit(X, y)\n", "slope = model.coef_[0]\n", "intercept = model.intercept_\n", "\n", "# 5. 打印回归结果\n", "print(f\"Regression Slope: {slope}\")\n", "print(f\"Regression Intercept: {intercept}\")\n", "\n", "# 6. 绘制散点图和回归线\n", "plt.scatter(x, y, color='blue', label='Data Points')\n", "plt.plot(x, model.predict(X), color='red', label='Regression Line')\n", "plt.xlabel('<PERSON><PERSON><PERSON>(Old)')\n", "plt.ylabel('<PERSON><PERSON><PERSON>(New)')\n", "plt.title('Linear Regression: <PERSON><PERSON><PERSON>(Old) vs <PERSON><PERSON><PERSON>(New)')\n", "plt.legend()\n", "plt.show()\n", "\n", "# 7. 进行斜率和截距的显著性检验\n", "# 使用t检验\n", "n = len(x)\n", "y_pred = model.predict(X)\n", "residuals = y - y_pred\n", "sse = np.sum(residuals**2)  # 残差平方和\n", "stderr = np.sqrt(sse / (n - 2)) / np.std(x)  # 标准误差\n", "\n", "# t检验\n", "t_stat = slope / stderr\n", "p_value = 2 * (1 - stats.t.cdf(np.abs(t_stat), df=n-2))\n", "print(f\"t-statistic: {t_stat}, p-value: {p_value}\")\n", "\n", "if p_value < 0.001:\n", "    print(\"The slope is significantly different from 0 (p < 0.001).\")\n", "else:\n", "    print(\"The slope is not significantly different from 0.\")\n", "\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [{"ename": "AttributeError", "evalue": "partially initialized module 'pandas' has no attribute '_pandas_parser_CAPI' (most likely due to a circular import)", "output_type": "error", "traceback": ["\u001b[31m---------------------------------------------------------------------------\u001b[39m", "\u001b[31mAttributeError\u001b[39m                            <PERSON><PERSON> (most recent call last)", "\u001b[36mCell\u001b[39m\u001b[36m \u001b[39m\u001b[32mIn[8]\u001b[39m\u001b[32m, line 1\u001b[39m\n\u001b[32m----> \u001b[39m\u001b[32m1\u001b[39m \u001b[38;5;28;01mimport\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34;01<PERSON><PERSON><PERSON>\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;28;01mas\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34;01mpd\u001b[39;00m\n\u001b[32m      2\u001b[39m \u001b[38;5;28;01mimport\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34;01mnumpy\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;28;01mas\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34;01mnp\u001b[39;00m\n\u001b[32m      3\u001b[39m \u001b[38;5;28;01mimport\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34;01mmatplotlib\u001b[39;00m\u001b[34;01m.\u001b[39;00m\u001b[34;01mpyplot\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;28;01mas\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34;01mplt\u001b[39;00m\n", "\u001b[36mFile \u001b[39m\u001b[32m~\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python311\\site-packages\\pandas\\__init__.py:151\u001b[39m\n\u001b[32m    132\u001b[39m \u001b[38;5;28;01mfrom\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34;01mpandas\u001b[39;00m\u001b[34;01m.\u001b[39;00m\u001b[34;01mcore\u001b[39;00m\u001b[34;01m.\u001b[39;00m\u001b[34;01mcomputation\u001b[39;00m\u001b[34;01m.\u001b[39;00m\u001b[34;01mapi\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;28;01mimport\u001b[39;00m \u001b[38;5;28meval\u001b[39m\n\u001b[32m    134\u001b[39m \u001b[38;5;28;01mfrom\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34;01mpandas\u001b[39;00m\u001b[34;01m.\u001b[39;00m\u001b[34;01mcore\u001b[39;00m\u001b[34;01m.\u001b[39;00m\u001b[34;01m<PERSON>ape\u001b[39;00m\u001b[34;01m.\u001b[39;00m\u001b[34;01mapi\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;28;01mimport\u001b[39;00m (\n\u001b[32m    135\u001b[39m     concat,\n\u001b[32m    136\u001b[39m     lreshape,\n\u001b[32m   (...)\u001b[39m\u001b[32m    148\u001b[39m     qcut,\n\u001b[32m    149\u001b[39m )\n\u001b[32m--> \u001b[39m\u001b[32m151\u001b[39m \u001b[38;5;28;01mfrom\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34;01mpandas\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;28;01mimport\u001b[39;00m api, arrays, errors, io, plotting, tseries\n\u001b[32m    152\u001b[39m \u001b[38;5;28;01mfrom\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34;01mpandas\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;28;01mimport\u001b[39;00m testing\n\u001b[32m    153\u001b[39m \u001b[38;5;28;01mfrom\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34;01mpandas\u001b[39;00m\u001b[34;01m.\u001b[39;00m\u001b[34;01mutil\u001b[39;00m\u001b[34;01m.\u001b[39;00m\u001b[34;01m_print_versions\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;28;01mimport\u001b[39;00m show_versions\n", "\u001b[36mFile \u001b[39m\u001b[32m~\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python311\\site-packages\\pandas\\api\\__init__.py:2\u001b[39m\n\u001b[32m      1\u001b[39m \u001b[33;03m\"\"\" public toolkit API \"\"\"\u001b[39;00m\n\u001b[32m----> \u001b[39m\u001b[32m2\u001b[39m \u001b[38;5;28;01mfrom\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34;01mpandas\u001b[39;00m\u001b[34;01m.\u001b[39;00m\u001b[34;01mapi\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;28;01mimport\u001b[39;00m (\n\u001b[32m      3\u001b[39m     extensions,\n\u001b[32m      4\u001b[39m     indexers,\n\u001b[32m      5\u001b[39m     interchange,\n\u001b[32m      6\u001b[39m     types,\n\u001b[32m      7\u001b[39m     typing,\n\u001b[32m      8\u001b[39m )\n\u001b[32m     10\u001b[39m __all__ = [\n\u001b[32m     11\u001b[39m     \u001b[33m\"\u001b[39m\u001b[33minterchange\u001b[39m\u001b[33m\"\u001b[39m,\n\u001b[32m     12\u001b[39m     \u001b[33m\"\u001b[39m\u001b[33mextensions\u001b[39m\u001b[33m\"\u001b[39m,\n\u001b[32m   (...)\u001b[39m\u001b[32m     15\u001b[39m     \u001b[33m\"\u001b[39m\u001b[33mtyping\u001b[39m\u001b[33m\"\u001b[39m,\n\u001b[32m     16\u001b[39m ]\n", "\u001b[36mFile \u001b[39m\u001b[32m~\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python311\\site-packages\\pandas\\api\\typing\\__init__.py:31\u001b[39m\n\u001b[32m     19\u001b[39m \u001b[38;5;28;01mfrom\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34;01mpandas\u001b[39;00m\u001b[34;01m.\u001b[39;00m\u001b[34;01mcore\u001b[39;00m\u001b[34;01m.\u001b[39;00m\u001b[34;01mwindow\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;28;01mimport\u001b[39;00m (\n\u001b[32m     20\u001b[39m     Expanding,\n\u001b[32m     21\u001b[39m     ExpandingGroupby,\n\u001b[32m   (...)\u001b[39m\u001b[32m     26\u001b[39m     Window,\n\u001b[32m     27\u001b[39m )\n\u001b[32m     29\u001b[39m \u001b[38;5;66;03m# TODO: Can't import Styler without importing jinja2\u001b[39;00m\n\u001b[32m     30\u001b[39m \u001b[38;5;66;03m# from pandas.io.formats.style import Styler\u001b[39;00m\n\u001b[32m---> \u001b[39m\u001b[32m31\u001b[39m \u001b[38;5;28;01mfrom\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34;01mpandas\u001b[39;00m\u001b[34;01m.\u001b[39;00m\u001b[34;01mio\u001b[39;00m\u001b[34;01m.\u001b[39;00m\u001b[34;01mjson\u001b[39;00m\u001b[34;01m.\u001b[39;00m\u001b[34;01m_json\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;28;01mimport\u001b[39;00m JsonReader\n\u001b[32m     32\u001b[39m \u001b[38;5;28;01mfrom\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34;01mpandas\u001b[39;00m\u001b[34;01m.\u001b[39;00m\u001b[34;01mio\u001b[39;00m\u001b[34;01m.\u001b[39;00m\u001b[34;01mstata\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;28;01mimport\u001b[39;00m StataReader\n\u001b[32m     34\u001b[39m __all__ = [\n\u001b[32m     35\u001b[39m     \u001b[33m\"\u001b[39m\u001b[33mDataFrameGroupBy\u001b[39m\u001b[33m\"\u001b[39m,\n\u001b[32m     36\u001b[39m     \u001b[33m\"\u001b[39m\u001b[33mDatetimeIndexResamplerGroupby\u001b[39m\u001b[33m\"\u001b[39m,\n\u001b[32m   (...)\u001b[39m\u001b[32m     54\u001b[39m     \u001b[33m\"\u001b[39m\u001b[33mWindow\u001b[39m\u001b[33m\"\u001b[39m,\n\u001b[32m     55\u001b[39m ]\n", "\u001b[36mFile \u001b[39m\u001b[32m~\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python311\\site-packages\\pandas\\io\\json\\__init__.py:1\u001b[39m\n\u001b[32m----> \u001b[39m\u001b[32m1\u001b[39m \u001b[38;5;28;01mfrom\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34;01mpandas\u001b[39;00m\u001b[34;01m.\u001b[39;00m\u001b[34;01mio\u001b[39;00m\u001b[34;01m.\u001b[39;00m\u001b[34;01mjson\u001b[39;00m\u001b[34;01m.\u001b[39;00m\u001b[34;01m_json\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;28;01mimport\u001b[39;00m (\n\u001b[32m      2\u001b[39m     read_json,\n\u001b[32m      3\u001b[39m     to_json,\n\u001b[32m      4\u001b[39m     ujson_dumps,\n\u001b[32m      5\u001b[39m     ujson_loads,\n\u001b[32m      6\u001b[39m )\n\u001b[32m      7\u001b[39m \u001b[38;5;28;01mfrom\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34;01mpandas\u001b[39;00m\u001b[34;01m.\u001b[39;00m\u001b[34;01mio\u001b[39;00m\u001b[34;01m.\u001b[39;00m\u001b[34;01mjson\u001b[39;00m\u001b[34;01m.\u001b[39;00m\u001b[34;01m_table_schema\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;28;01mimport\u001b[39;00m build_table_schema\n\u001b[32m      9\u001b[39m __all__ = [\n\u001b[32m     10\u001b[39m     \u001b[33m\"\u001b[39m\u001b[33mujson_dumps\u001b[39m\u001b[33m\"\u001b[39m,\n\u001b[32m     11\u001b[39m     \u001b[33m\"\u001b[39m\u001b[33mujson_loads\u001b[39m\u001b[33m\"\u001b[39m,\n\u001b[32m   (...)\u001b[39m\u001b[32m     14\u001b[39m     \u001b[33m\"\u001b[39m\u001b[33mbuild_table_schema\u001b[39m\u001b[33m\"\u001b[39m,\n\u001b[32m     15\u001b[39m ]\n", "\u001b[36mFile \u001b[39m\u001b[32m~\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python311\\site-packages\\pandas\\io\\json\\_json.py:71\u001b[39m\n\u001b[32m     66\u001b[39m \u001b[38;5;28;01mfrom\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34;01mpandas\u001b[39;00m\u001b[34;01m.\u001b[39;00m\u001b[34;01mio\u001b[39;00m\u001b[34;01m.\u001b[39;00m\u001b[34;01mjson\u001b[39;00m\u001b[34;01m.\u001b[39;00m\u001b[34;01m_normalize\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;28;01mimport\u001b[39;00m convert_to_line_delimits\n\u001b[32m     67\u001b[39m \u001b[38;5;28;01mfrom\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34;01mpandas\u001b[39;00m\u001b[34;01m.\u001b[39;00m\u001b[34;01mio\u001b[39;00m\u001b[34;01m.\u001b[39;00m\u001b[34;01mj<PERSON>\u001b[39;00m\u001b[34;01m.\u001b[39;00m\u001b[34;01m_table_schema\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;28;01mimport\u001b[39;00m (\n\u001b[32m     68\u001b[39m     build_table_schema,\n\u001b[32m     69\u001b[39m     parse_table_schema,\n\u001b[32m     70\u001b[39m )\n\u001b[32m---> \u001b[39m\u001b[32m71\u001b[39m \u001b[38;5;28;01mfrom\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34;01mpandas\u001b[39;00m\u001b[34;01m.\u001b[39;00m\u001b[34;01mio\u001b[39;00m\u001b[34;01m.\u001b[39;00m\u001b[34;01mparsers\u001b[39;00m\u001b[34;01m.\u001b[39;00m\u001b[34;01mreaders\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;28;01mimport\u001b[39;00m validate_integer\n\u001b[32m     73\u001b[39m \u001b[38;5;28;01mif\u001b[39;00m TYPE_CHECKING:\n\u001b[32m     74\u001b[39m     \u001b[38;5;28;01mfrom\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34;01mcollections\u001b[39;00m\u001b[34;01m.\u001b[39;00m\u001b[34;01mabc\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;28;01mimport\u001b[39;00m (\n\u001b[32m     75\u001b[39m         Hashable,\n\u001b[32m     76\u001b[39m         Mapping,\n\u001b[32m     77\u001b[39m     )\n", "\u001b[36mFile \u001b[39m\u001b[32m~\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python311\\site-packages\\pandas\\io\\parsers\\__init__.py:1\u001b[39m\n\u001b[32m----> \u001b[39m\u001b[32m1\u001b[39m \u001b[38;5;28;01mfrom\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34;01mpandas\u001b[39;00m\u001b[34;01m.\u001b[39;00m\u001b[34;01mio\u001b[39;00m\u001b[34;01m.\u001b[39;00m\u001b[34;01mparsers\u001b[39;00m\u001b[34;01m.\u001b[39;00m\u001b[34;01mreaders\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;28;01mimport\u001b[39;00m (\n\u001b[32m      2\u001b[39m     TextFileReader,\n\u001b[32m      3\u001b[39m     TextParser,\n\u001b[32m      4\u001b[39m     read_csv,\n\u001b[32m      5\u001b[39m     read_fwf,\n\u001b[32m      6\u001b[39m     read_table,\n\u001b[32m      7\u001b[39m )\n\u001b[32m      9\u001b[39m __all__ = [\u001b[33m\"\u001b[39m\u001b[33mTextFileReader\u001b[39m\u001b[33m\"\u001b[39m, \u001b[33m\"\u001b[39m\u001b[33mTextParser\u001b[39m\u001b[33m\"\u001b[39m, \u001b[33m\"\u001b[39m\u001b[33mread_csv\u001b[39m\u001b[33m\"\u001b[39m, \u001b[33m\"\u001b[39m\u001b[33mread_fwf\u001b[39m\u001b[33m\"\u001b[39m, \u001b[33m\"\u001b[39m\u001b[33mread_table\u001b[39m\u001b[33m\"\u001b[39m]\n", "\u001b[36mFile \u001b[39m\u001b[32m~\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python311\\site-packages\\pandas\\io\\parsers\\readers.py:32\u001b[39m\n\u001b[32m     29\u001b[39m \u001b[38;5;28;01mfrom\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34;01mpanda<PERSON>\u001b[39;00m\u001b[34;01m.\u001b[39;00m\u001b[34;01m_config\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;28;01mimport\u001b[39;00m using_copy_on_write\n\u001b[32m     31\u001b[39m \u001b[38;5;28;01mfrom\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34;01mpandas\u001b[39;00m\u001b[34;01m.\u001b[39;00m\u001b[34;01m_libs\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;28;01mimport\u001b[39;00m lib\n\u001b[32m---> \u001b[39m\u001b[32m32\u001b[39m \u001b[38;5;28;01mfrom\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34;01mpandas\u001b[39;00m\u001b[34;01m.\u001b[39;00m\u001b[34;01m_libs\u001b[39;00m\u001b[34;01m.\u001b[39;00m\u001b[34;01mparsers\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;28;01mimport\u001b[39;00m STR_NA_VALUES\n\u001b[32m     33\u001b[39m \u001b[38;5;28;01mfrom\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34;01mpandas\u001b[39;00m\u001b[34;01m.\u001b[39;00m\u001b[34;01merrors\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;28;01mimport\u001b[39;00m (\n\u001b[32m     34\u001b[39m     AbstractMethodError,\n\u001b[32m     35\u001b[39m     ParserWarning,\n\u001b[32m     36\u001b[39m )\n\u001b[32m     37\u001b[39m \u001b[38;5;28;01mfrom\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34;01mpandas\u001b[39;00m\u001b[34;01m.\u001b[39;00m\u001b[34;01mutil\u001b[39;00m\u001b[34;01m.\u001b[39;00m\u001b[34;01m_decorators\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;28;01mimport\u001b[39;00m Appender\n", "\u001b[36mFile \u001b[39m\u001b[32mparsers.pyx:1418\u001b[39m, in \u001b[36minit pandas._libs.parsers\u001b[39m\u001b[34m()\u001b[39m\n", "\u001b[31mAttributeError\u001b[39m: partially initialized module 'pandas' has no attribute '_pandas_parser_CAPI' (most likely due to a circular import)"]}], "source": ["import pandas as pd\n", "import numpy as np\n", "import matplotlib.pyplot as plt\n", "# import seaborn as sns\n", "import scipy.stats as stats\n", "from sklearn.linear_model import LinearRegression\n", "import os\n", "\n", "\n", "os.chdir(\"D:\\E3A\\亿杉算法实习\\温度补偿\\医院临床测试记录\")\n", "# 1. 加载数据\n", "file_path = \"4.25记录 _除去异常.xlsx\"  # 这里替换为你的文件路径\n", "data = pd.read_excel(file_path)\n", "\n", "# 2. 提取相关的列（假设\"YSJ(Old)\"和\"YSJ(New)\"是数据中的列名）\n", "x = data['YSJ20(Old)']  # 自变量\n", "y = data['YSJ20(New)']  # 因变量\n", "\n", "# 3. 计算皮尔逊相关系数\n", "correlation, _ = stats.pearsonr(x, y)\n", "print(f\"Pearson correlation coefficient: {correlation}\")\n", "\n", "# 4. 进行线性回归分析\n", "X = x.values.reshape(-1, 1)  # 自变量，需要reshape成二维数组\n", "y = y.values\n", "\n", "model = LinearRegression()\n", "model.fit(X, y)\n", "slope = model.coef_[0]\n", "intercept = model.intercept_\n", "\n", "# 5. 打印回归结果\n", "print(f\"Regression Slope: {slope}\")\n", "print(f\"Regression Intercept: {intercept}\")\n", "\n", "# 6. 绘制散点图和回归线\n", "plt.scatter(x, y, color='blue', label='Data Points')\n", "plt.plot(x, model.predict(X), color='red', label='Regression Line')\n", "plt.xlabel('<PERSON><PERSON><PERSON>(Old)')\n", "plt.ylabel('Dw')\n", "plt.title('Linear Regression: <PERSON><PERSON><PERSON>(Old) vs Dw')\n", "plt.legend()\n", "plt.show()\n", "\n", "# 7. 进行斜率和截距的显著性检验\n", "# 使用t检验\n", "n = len(x)\n", "y_pred = model.predict(X)\n", "residuals = y - y_pred\n", "sse = np.sum(residuals**2)  # 残差平方和\n", "stderr = np.sqrt(sse / (n - 2)) / np.std(x)  # 标准误差\n", "\n", "# t检验\n", "t_stat = slope / stderr\n", "p_value = 2 * (1 - stats.t.cdf(np.abs(t_stat), df=n-2))\n", "print(f\"t-statistic: {t_stat}, p-value: {p_value}\")\n", "\n", "if p_value < 0.001:\n", "    print(\"The slope is significantly different from 0 (p < 0.001).\")\n", "else:\n", "    print(\"The slope is not significantly different from 0.\")\n", "\n"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [{"ename": "AttributeError", "evalue": "partially initialized module 'pandas' has no attribute '_pandas_parser_CAPI' (most likely due to a circular import)", "output_type": "error", "traceback": ["\u001b[31m---------------------------------------------------------------------------\u001b[39m", "\u001b[31mAttributeError\u001b[39m                            <PERSON><PERSON> (most recent call last)", "\u001b[36mCell\u001b[39m\u001b[36m \u001b[39m\u001b[32mIn[9]\u001b[39m\u001b[32m, line 1\u001b[39m\n\u001b[32m----> \u001b[39m\u001b[32m1\u001b[39m \u001b[38;5;28;01mimport\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34;01<PERSON><PERSON><PERSON>\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;28;01mas\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34;01mpd\u001b[39;00m\n\u001b[32m      2\u001b[39m \u001b[38;5;28;01mimport\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34;01mnumpy\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;28;01mas\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34;01mnp\u001b[39;00m\n\u001b[32m      3\u001b[39m \u001b[38;5;28;01mimport\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34;01mmatplotlib\u001b[39;00m\u001b[34;01m.\u001b[39;00m\u001b[34;01mpyplot\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;28;01mas\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34;01mplt\u001b[39;00m\n", "\u001b[36mFile \u001b[39m\u001b[32m~\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python311\\site-packages\\pandas\\__init__.py:151\u001b[39m\n\u001b[32m    132\u001b[39m \u001b[38;5;28;01mfrom\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34;01mpandas\u001b[39;00m\u001b[34;01m.\u001b[39;00m\u001b[34;01mcore\u001b[39;00m\u001b[34;01m.\u001b[39;00m\u001b[34;01mcomputation\u001b[39;00m\u001b[34;01m.\u001b[39;00m\u001b[34;01mapi\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;28;01mimport\u001b[39;00m \u001b[38;5;28meval\u001b[39m\n\u001b[32m    134\u001b[39m \u001b[38;5;28;01mfrom\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34;01mpandas\u001b[39;00m\u001b[34;01m.\u001b[39;00m\u001b[34;01mcore\u001b[39;00m\u001b[34;01m.\u001b[39;00m\u001b[34;01m<PERSON>ape\u001b[39;00m\u001b[34;01m.\u001b[39;00m\u001b[34;01mapi\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;28;01mimport\u001b[39;00m (\n\u001b[32m    135\u001b[39m     concat,\n\u001b[32m    136\u001b[39m     lreshape,\n\u001b[32m   (...)\u001b[39m\u001b[32m    148\u001b[39m     qcut,\n\u001b[32m    149\u001b[39m )\n\u001b[32m--> \u001b[39m\u001b[32m151\u001b[39m \u001b[38;5;28;01mfrom\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34;01mpandas\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;28;01mimport\u001b[39;00m api, arrays, errors, io, plotting, tseries\n\u001b[32m    152\u001b[39m \u001b[38;5;28;01mfrom\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34;01mpandas\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;28;01mimport\u001b[39;00m testing\n\u001b[32m    153\u001b[39m \u001b[38;5;28;01mfrom\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34;01mpandas\u001b[39;00m\u001b[34;01m.\u001b[39;00m\u001b[34;01mutil\u001b[39;00m\u001b[34;01m.\u001b[39;00m\u001b[34;01m_print_versions\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;28;01mimport\u001b[39;00m show_versions\n", "\u001b[36mFile \u001b[39m\u001b[32m~\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python311\\site-packages\\pandas\\api\\__init__.py:2\u001b[39m\n\u001b[32m      1\u001b[39m \u001b[33;03m\"\"\" public toolkit API \"\"\"\u001b[39;00m\n\u001b[32m----> \u001b[39m\u001b[32m2\u001b[39m \u001b[38;5;28;01mfrom\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34;01mpandas\u001b[39;00m\u001b[34;01m.\u001b[39;00m\u001b[34;01mapi\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;28;01mimport\u001b[39;00m (\n\u001b[32m      3\u001b[39m     extensions,\n\u001b[32m      4\u001b[39m     indexers,\n\u001b[32m      5\u001b[39m     interchange,\n\u001b[32m      6\u001b[39m     types,\n\u001b[32m      7\u001b[39m     typing,\n\u001b[32m      8\u001b[39m )\n\u001b[32m     10\u001b[39m __all__ = [\n\u001b[32m     11\u001b[39m     \u001b[33m\"\u001b[39m\u001b[33minterchange\u001b[39m\u001b[33m\"\u001b[39m,\n\u001b[32m     12\u001b[39m     \u001b[33m\"\u001b[39m\u001b[33mextensions\u001b[39m\u001b[33m\"\u001b[39m,\n\u001b[32m   (...)\u001b[39m\u001b[32m     15\u001b[39m     \u001b[33m\"\u001b[39m\u001b[33mtyping\u001b[39m\u001b[33m\"\u001b[39m,\n\u001b[32m     16\u001b[39m ]\n", "\u001b[36mFile \u001b[39m\u001b[32m~\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python311\\site-packages\\pandas\\api\\typing\\__init__.py:31\u001b[39m\n\u001b[32m     19\u001b[39m \u001b[38;5;28;01mfrom\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34;01mpandas\u001b[39;00m\u001b[34;01m.\u001b[39;00m\u001b[34;01mcore\u001b[39;00m\u001b[34;01m.\u001b[39;00m\u001b[34;01mwindow\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;28;01mimport\u001b[39;00m (\n\u001b[32m     20\u001b[39m     Expanding,\n\u001b[32m     21\u001b[39m     ExpandingGroupby,\n\u001b[32m   (...)\u001b[39m\u001b[32m     26\u001b[39m     Window,\n\u001b[32m     27\u001b[39m )\n\u001b[32m     29\u001b[39m \u001b[38;5;66;03m# TODO: Can't import Styler without importing jinja2\u001b[39;00m\n\u001b[32m     30\u001b[39m \u001b[38;5;66;03m# from pandas.io.formats.style import Styler\u001b[39;00m\n\u001b[32m---> \u001b[39m\u001b[32m31\u001b[39m \u001b[38;5;28;01mfrom\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34;01mpandas\u001b[39;00m\u001b[34;01m.\u001b[39;00m\u001b[34;01mio\u001b[39;00m\u001b[34;01m.\u001b[39;00m\u001b[34;01mjson\u001b[39;00m\u001b[34;01m.\u001b[39;00m\u001b[34;01m_json\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;28;01mimport\u001b[39;00m JsonReader\n\u001b[32m     32\u001b[39m \u001b[38;5;28;01mfrom\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34;01mpandas\u001b[39;00m\u001b[34;01m.\u001b[39;00m\u001b[34;01mio\u001b[39;00m\u001b[34;01m.\u001b[39;00m\u001b[34;01mstata\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;28;01mimport\u001b[39;00m StataReader\n\u001b[32m     34\u001b[39m __all__ = [\n\u001b[32m     35\u001b[39m     \u001b[33m\"\u001b[39m\u001b[33mDataFrameGroupBy\u001b[39m\u001b[33m\"\u001b[39m,\n\u001b[32m     36\u001b[39m     \u001b[33m\"\u001b[39m\u001b[33mDatetimeIndexResamplerGroupby\u001b[39m\u001b[33m\"\u001b[39m,\n\u001b[32m   (...)\u001b[39m\u001b[32m     54\u001b[39m     \u001b[33m\"\u001b[39m\u001b[33mWindow\u001b[39m\u001b[33m\"\u001b[39m,\n\u001b[32m     55\u001b[39m ]\n", "\u001b[36mFile \u001b[39m\u001b[32m~\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python311\\site-packages\\pandas\\io\\json\\__init__.py:1\u001b[39m\n\u001b[32m----> \u001b[39m\u001b[32m1\u001b[39m \u001b[38;5;28;01mfrom\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34;01mpandas\u001b[39;00m\u001b[34;01m.\u001b[39;00m\u001b[34;01mio\u001b[39;00m\u001b[34;01m.\u001b[39;00m\u001b[34;01mjson\u001b[39;00m\u001b[34;01m.\u001b[39;00m\u001b[34;01m_json\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;28;01mimport\u001b[39;00m (\n\u001b[32m      2\u001b[39m     read_json,\n\u001b[32m      3\u001b[39m     to_json,\n\u001b[32m      4\u001b[39m     ujson_dumps,\n\u001b[32m      5\u001b[39m     ujson_loads,\n\u001b[32m      6\u001b[39m )\n\u001b[32m      7\u001b[39m \u001b[38;5;28;01mfrom\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34;01mpandas\u001b[39;00m\u001b[34;01m.\u001b[39;00m\u001b[34;01mio\u001b[39;00m\u001b[34;01m.\u001b[39;00m\u001b[34;01mjson\u001b[39;00m\u001b[34;01m.\u001b[39;00m\u001b[34;01m_table_schema\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;28;01mimport\u001b[39;00m build_table_schema\n\u001b[32m      9\u001b[39m __all__ = [\n\u001b[32m     10\u001b[39m     \u001b[33m\"\u001b[39m\u001b[33mujson_dumps\u001b[39m\u001b[33m\"\u001b[39m,\n\u001b[32m     11\u001b[39m     \u001b[33m\"\u001b[39m\u001b[33mujson_loads\u001b[39m\u001b[33m\"\u001b[39m,\n\u001b[32m   (...)\u001b[39m\u001b[32m     14\u001b[39m     \u001b[33m\"\u001b[39m\u001b[33mbuild_table_schema\u001b[39m\u001b[33m\"\u001b[39m,\n\u001b[32m     15\u001b[39m ]\n", "\u001b[36mFile \u001b[39m\u001b[32m~\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python311\\site-packages\\pandas\\io\\json\\_json.py:71\u001b[39m\n\u001b[32m     66\u001b[39m \u001b[38;5;28;01mfrom\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34;01mpandas\u001b[39;00m\u001b[34;01m.\u001b[39;00m\u001b[34;01mio\u001b[39;00m\u001b[34;01m.\u001b[39;00m\u001b[34;01mjson\u001b[39;00m\u001b[34;01m.\u001b[39;00m\u001b[34;01m_normalize\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;28;01mimport\u001b[39;00m convert_to_line_delimits\n\u001b[32m     67\u001b[39m \u001b[38;5;28;01mfrom\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34;01mpandas\u001b[39;00m\u001b[34;01m.\u001b[39;00m\u001b[34;01mio\u001b[39;00m\u001b[34;01m.\u001b[39;00m\u001b[34;01mj<PERSON>\u001b[39;00m\u001b[34;01m.\u001b[39;00m\u001b[34;01m_table_schema\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;28;01mimport\u001b[39;00m (\n\u001b[32m     68\u001b[39m     build_table_schema,\n\u001b[32m     69\u001b[39m     parse_table_schema,\n\u001b[32m     70\u001b[39m )\n\u001b[32m---> \u001b[39m\u001b[32m71\u001b[39m \u001b[38;5;28;01mfrom\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34;01mpandas\u001b[39;00m\u001b[34;01m.\u001b[39;00m\u001b[34;01mio\u001b[39;00m\u001b[34;01m.\u001b[39;00m\u001b[34;01mparsers\u001b[39;00m\u001b[34;01m.\u001b[39;00m\u001b[34;01mreaders\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;28;01mimport\u001b[39;00m validate_integer\n\u001b[32m     73\u001b[39m \u001b[38;5;28;01mif\u001b[39;00m TYPE_CHECKING:\n\u001b[32m     74\u001b[39m     \u001b[38;5;28;01mfrom\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34;01mcollections\u001b[39;00m\u001b[34;01m.\u001b[39;00m\u001b[34;01mabc\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;28;01mimport\u001b[39;00m (\n\u001b[32m     75\u001b[39m         Hashable,\n\u001b[32m     76\u001b[39m         Mapping,\n\u001b[32m     77\u001b[39m     )\n", "\u001b[36mFile \u001b[39m\u001b[32m~\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python311\\site-packages\\pandas\\io\\parsers\\__init__.py:1\u001b[39m\n\u001b[32m----> \u001b[39m\u001b[32m1\u001b[39m \u001b[38;5;28;01mfrom\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34;01mpandas\u001b[39;00m\u001b[34;01m.\u001b[39;00m\u001b[34;01mio\u001b[39;00m\u001b[34;01m.\u001b[39;00m\u001b[34;01mparsers\u001b[39;00m\u001b[34;01m.\u001b[39;00m\u001b[34;01mreaders\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;28;01mimport\u001b[39;00m (\n\u001b[32m      2\u001b[39m     TextFileReader,\n\u001b[32m      3\u001b[39m     TextParser,\n\u001b[32m      4\u001b[39m     read_csv,\n\u001b[32m      5\u001b[39m     read_fwf,\n\u001b[32m      6\u001b[39m     read_table,\n\u001b[32m      7\u001b[39m )\n\u001b[32m      9\u001b[39m __all__ = [\u001b[33m\"\u001b[39m\u001b[33mTextFileReader\u001b[39m\u001b[33m\"\u001b[39m, \u001b[33m\"\u001b[39m\u001b[33mTextParser\u001b[39m\u001b[33m\"\u001b[39m, \u001b[33m\"\u001b[39m\u001b[33mread_csv\u001b[39m\u001b[33m\"\u001b[39m, \u001b[33m\"\u001b[39m\u001b[33mread_fwf\u001b[39m\u001b[33m\"\u001b[39m, \u001b[33m\"\u001b[39m\u001b[33mread_table\u001b[39m\u001b[33m\"\u001b[39m]\n", "\u001b[36mFile \u001b[39m\u001b[32m~\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python311\\site-packages\\pandas\\io\\parsers\\readers.py:32\u001b[39m\n\u001b[32m     29\u001b[39m \u001b[38;5;28;01mfrom\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34;01mpanda<PERSON>\u001b[39;00m\u001b[34;01m.\u001b[39;00m\u001b[34;01m_config\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;28;01mimport\u001b[39;00m using_copy_on_write\n\u001b[32m     31\u001b[39m \u001b[38;5;28;01mfrom\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34;01mpandas\u001b[39;00m\u001b[34;01m.\u001b[39;00m\u001b[34;01m_libs\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;28;01mimport\u001b[39;00m lib\n\u001b[32m---> \u001b[39m\u001b[32m32\u001b[39m \u001b[38;5;28;01mfrom\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34;01mpandas\u001b[39;00m\u001b[34;01m.\u001b[39;00m\u001b[34;01m_libs\u001b[39;00m\u001b[34;01m.\u001b[39;00m\u001b[34;01mparsers\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;28;01mimport\u001b[39;00m STR_NA_VALUES\n\u001b[32m     33\u001b[39m \u001b[38;5;28;01mfrom\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34;01mpandas\u001b[39;00m\u001b[34;01m.\u001b[39;00m\u001b[34;01merrors\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;28;01mimport\u001b[39;00m (\n\u001b[32m     34\u001b[39m     AbstractMethodError,\n\u001b[32m     35\u001b[39m     ParserWarning,\n\u001b[32m     36\u001b[39m )\n\u001b[32m     37\u001b[39m \u001b[38;5;28;01mfrom\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34;01mpandas\u001b[39;00m\u001b[34;01m.\u001b[39;00m\u001b[34;01mutil\u001b[39;00m\u001b[34;01m.\u001b[39;00m\u001b[34;01m_decorators\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;28;01mimport\u001b[39;00m Appender\n", "\u001b[36mFile \u001b[39m\u001b[32mparsers.pyx:1418\u001b[39m, in \u001b[36minit pandas._libs.parsers\u001b[39m\u001b[34m()\u001b[39m\n", "\u001b[31mAttributeError\u001b[39m: partially initialized module 'pandas' has no attribute '_pandas_parser_CAPI' (most likely due to a circular import)"]}], "source": ["import pandas as pd\n", "import numpy as np\n", "import matplotlib.pyplot as plt\n", "# import seaborn as sns\n", "import scipy.stats as stats\n", "from sklearn.linear_model import LinearRegression\n", "import os\n", "\n", "\n", "os.chdir(\"D:\\E3A\\亿杉算法实习\\温度补偿\\医院临床测试记录\")\n", "# 1. 加载数据\n", "file_path = \"4.25记录 _除去异常.xlsx\"  # 这里替换为你的文件路径\n", "data = pd.read_excel(file_path)\n", "\n", "# 2. 提取相关的列（假设\"YSJ(Old)\"和\"YSJ(New)\"是数据中的列名）\n", "x = data['YSJ20(New)']  # 自变量\n", "y = data['戴维（胸前）']  # 因变量\n", "\n", "# 3. 计算皮尔逊相关系数\n", "correlation, _ = stats.pearsonr(x, y)\n", "print(f\"Pearson correlation coefficient: {correlation}\")\n", "\n", "# 4. 进行线性回归分析\n", "X = x.values.reshape(-1, 1)  # 自变量，需要reshape成二维数组\n", "y = y.values\n", "\n", "model = LinearRegression()\n", "model.fit(X, y)\n", "slope = model.coef_[0]\n", "intercept = model.intercept_\n", "\n", "# 5. 打印回归结果\n", "print(f\"Regression Slope: {slope}\")\n", "print(f\"Regression Intercept: {intercept}\")\n", "\n", "# 6. 绘制散点图和回归线\n", "plt.scatter(x, y, color='blue', label='Data Points')\n", "plt.plot(x, model.predict(X), color='red', label='Regression Line')\n", "plt.xlabel('<PERSON><PERSON><PERSON>(New)')\n", "plt.ylabel('Dw')\n", "plt.title('Linear Regression: <PERSON><PERSON><PERSON>(New) vs <PERSON><PERSON>')\n", "plt.legend()\n", "plt.show()\n", "\n", "# 7. 进行斜率和截距的显著性检验\n", "# 使用t检验\n", "n = len(x)\n", "y_pred = model.predict(X)\n", "residuals = y - y_pred\n", "sse = np.sum(residuals**2)  # 残差平方和\n", "stderr = np.sqrt(sse / (n - 2)) / np.std(x)  # 标准误差\n", "\n", "# t检验\n", "t_stat = slope / stderr\n", "p_value = 2 * (1 - stats.t.cdf(np.abs(t_stat), df=n-2))\n", "print(f\"t-statistic: {t_stat}, p-value: {p_value}\")\n", "\n", "if p_value < 0.001:\n", "    print(\"The slope is significantly different from 0 (p < 0.001).\")\n", "else:\n", "    print(\"The slope is not significantly different from 0.\")\n", "\n"]}, {"cell_type": "code", "execution_count": 35, "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 800x600 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["Regression Slope: 0.24572426253070204\n", "Regression Intercept: -0.21979568803651928\n", "p-value for the slope: 0.010955076343633638\n", "The slope is not significantly different from 0.\n"]}], "source": ["import pandas as pd\n", "import numpy as np\n", "import matplotlib.pyplot as plt\n", "from scipy import stats\n", "import os\n", "\n", "\n", "os.chdir(\"D:\\E3A\\亿杉算法实习\\温度补偿\\医院临床测试记录\")\n", "# 1. 加载数据\n", "file_path = \"4.25记录 _除去异常.xlsx\"  # 这里替换为你的文件路径\n", "data = pd.read_excel(file_path)\n", "\n", "\n", "# 2. 提取“YSJ(Old)”和“YSJ(New)”列\n", "x = data['YSJ20(Old)']  # 自变量\n", "y = data['YSJ20(New)']  # 因变量\n", "\n", "# 3. 计算差异和平均值\n", "diff = x - y  # 差异\n", "mean = (x + y) / 2  # 平均值\n", "\n", "# 4. 计算95%的限度（Bland-Altman Limits）\n", "mean_diff = np.mean(diff)\n", "std_diff = np.std(diff)\n", "upper_limit = mean_diff + 1.96 * std_diff\n", "lower_limit = mean_diff - 1.96 * std_diff\n", "\n", "# 5. 绘制Bland-Alt<PERSON>图\n", "plt.figure(figsize=(8,6))\n", "plt.scatter(mean, diff, color='black', alpha=0.5)\n", "plt.axhline(mean_diff, color='blue', linestyle='--', label=f'Mean Difference: {mean_diff:.2f}')\n", "plt.axhline(upper_limit, color='red', linestyle='--', label=f'Upper Limit: {upper_limit:.2f}')\n", "plt.axhline(lower_limit, color='red', linestyle='--', label=f'Lower Limit: {lower_limit:.2f}')\n", "plt.xlabel('Mean of YSJ(Old) and YS<PERSON>(New)')\n", "plt.ylabel('Difference between <PERSON>S<PERSON>(Old) and Y<PERSON><PERSON>(New)')\n", "plt.title('Bland-Altman Plot: <PERSON><PERSON><PERSON>(Old) vs <PERSON><PERSON><PERSON>(New)')\n", "plt.legend()\n", "plt.show()\n", "\n", "# 6. 进行回归分析（差异与平均值之间的关系）\n", "slope, intercept, r_value, p_value, std_err = stats.linregress(mean, diff)\n", "print(f\"Regression Slope: {slope}\")\n", "print(f\"Regression Intercept: {intercept}\")\n", "print(f\"p-value for the slope: {p_value}\")\n", "\n", "# 检查斜率和截距是否显著\n", "if p_value < 0.001:\n", "    print(\"The slope is significantly different from 0 (p < 0.001).\")\n", "else:\n", "    print(\"The slope is not significantly different from 0.\")\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["R² Value: 0.7717794337015983\n", "<PERSON>'s f²: 3.381726047829035\n", "Current power with 60 samples: 1.0\n"]}], "source": ["import numpy as np\n", "import pandas as pd\n", "from statsmodels.stats.power import TTestIndPower\n", "from sklearn.linear_model import LinearRegression\n", "import os\n", "\n", "\n", "os.chdir(\"D:\\E3A\\亿杉算法实习\\温度补偿\\医院临床测试记录\")\n", "# 1. 加载数据\n", "file_path = \"4.25记录.xlsx\"  # 这里替换为你的文件路径\n", "data = pd.read_excel(file_path)\n", "\n", "\n", "# 2. 提取“YSJ(Old)”和“YSJ(New)”列\n", "x = data['YSJ20(Old)']  # 自变量\n", "y = data['YSJ20(New)']  # 因变量\n", "\n", "# 2. 计算相关性和R²（回归模型的拟合优度）\n", "# 进行线性回归\n", "regressor = LinearRegression()\n", "X = x.values.reshape(-1, 1)  # 需要reshape为二维数组\n", "regressor.fit(X, y)\n", "\n", "# 获取回归模型的R²值\n", "r_squared = regressor.score(X, y)\n", "print(f\"R² Value: {r_squared}\")\n", "\n", "# 3. 计算效应大小（<PERSON>'s f²）\n", "f_squared = r_squared / (1 - r_squared)  # <PERSON>'s f² = R² / (1 - R²)\n", "print(f\"<PERSON>'s f²: {f_squared}\")\n", "\n", "# 4. 使用现有样本量计算功效\n", "sample_size = 60  # 你的样本量\n", "alpha = 0.05  # 显著性水平\n", "\n", "# 5. 进行功效分析，计算功效\n", "power_analysis = TTestIndPower()\n", "current_power = power_analysis.solve_power(effect_size=f_squared, nobs1=sample_size, alpha=alpha)\n", "\n", "print(f\"Current power with {sample_size} samples: {current_power}\")\n"]}, {"cell_type": "code", "execution_count": 19, "metadata": {}, "outputs": [{"data": {"image/png": "iVBORw0KGgoAAAANSUhEUgAAArEAAAIjCAYAAAAUdENlAAAAOXRFWHRTb2Z0d2FyZQBNYXRwbG90bGliIHZlcnNpb24zLjUuMiwgaHR0cHM6Ly9tYXRwbG90bGliLm9yZy8qNh9FAAAACXBIWXMAAA9hAAAPYQGoP6dpAACKu0lEQVR4nOzdeVhUddsH8O8wCAPIJgMKAuIWDK6QuSuYlktm+JhZ6uOS2eKCplaaWy65Wy6VSz5immZWQmXlDopaaoa4DLglgrggKMMm65z3D94ZHYFxBmaYGfh+rmuuyznnPmfuOWc43nPmt4gEQRBARERERGRBrEydABERERGRvljEEhEREZHFYRFLRERERBaHRSwRERERWRwWsURERERkcVjEEhEREZHFYRFLRERERBaHRSwRERERWRwWsURERERkcVjEEpkxkUiETz75xCSv7efnh1GjRhl0n1u2bIFIJEJSUpJB92spTHk+q9uuXbtQr1495OTkmDqVCk2fPh0dOnQwdRplxMTEQCQSISYmxtSpEJk1FrFE1UhVxD3+8PDwQI8ePfDHH3+YOr0qa9++PUQiEdatW6fzNl999RW2bNlivKSMLCkpSeN8isVi+Pr6YuDAgTh79qxBXkMul+OTTz6pdPF//fp12Nvb44033ih3/ffffw+RSIQvv/xSvezXX39FSEgIPDw8YG9vjyZNmuC1117D3r171TGq975ixQqN/ZWUlGDu3LmYOHEi6tatq17u5+cHkUiEiRMnlslBVbj9+OOPlXqPlTF58mTEx8fjl19+qbbXNKQnrycSiQReXl7o3bs31qxZg+zsbFOnSGRULGKJTGD+/PnYtm0btm7dig8//BD37t1Dv379sGfPHlOnVmlXrlzB6dOn4efnh+3bt+u8naUXsSpvvPEGtm3bhs2bN2Po0KE4fPgwOnbsaJBCVi6XY968eZUuYhs3boy5c+di586d2L9/v8a6rKwsvP/+++jQoQPee+89AMCKFSswYMAAiEQizJgxA59//jkGDRqEK1euYOfOnU99vV9//RWXLl3C22+/Xe76r7/+Grdu3arUezGkBg0a4JVXXilThFsa1fVk3bp16i8IkydPRqtWrXDu3DkTZ0dkPNamToCoNurbty/atWunfj5mzBjUr18f3333Hfr372/CzCrv22+/hYeHB1auXIlXX30VSUlJ8PPzM3Va1SY4OBjDhw9XP+/SpQsGDBiAdevWYcOGDSbMrNTUqVOxfft2jBs3DufPn4ednR0AYObMmbh37x727t0LKysrFBcXY8GCBXjhhRfKFLwAkJaW9tTXioiIQJcuXdCwYcMy61q0aIFLly5hyZIlWLNmTdXfWBW99tprGDx4MP799180adLE1OlUypPXkxkzZuDw4cPo378/BgwYgISEBPX5JqpJeCeWyAy4uLjAzs4O1tbav1feuHED48aNg7+/P+zs7ODm5obBgweXuUOn+pnx+PHjmDJlCtzd3eHg4ICBAwfi3r17GrGCIGDhwoXw9vaGvb09evTogYsXL+r9Hnbs2IFXX30V/fv3h7OzM3bs2PHUbfz8/HDx4kUcOXJE/ZNoaGioxns4duwYwsPD4e7uDhcXF7zzzjsoLCxEZmYmRowYAVdXV7i6uuLDDz+EIAga+1+xYgU6d+4MNzc32NnZ4dlnny3352qRSIQJEyYgKioKLVu2hK2tLVq0aKHx07m+nn/+eQClP+VrExcXh759+8LJyQl169ZFz5498ddff6nXb9myBYMHDwYA9OjRQ32cVO0lFQoFEhMToVAotL6OtbU1Nm7ciOvXr2PhwoUAgDNnzuCrr77C1KlT0bp1awBAeno6srKy0KVLl3L34+HhofV18vPzsXfvXvTq1avc9X5+fhgxYoTOd2NTU1Px5ptvon79+urzsnnzZvV6QRAglUoxZcoU9TKlUgkXFxeIxWJkZmaqly9duhTW1tYa7XRVef78889PzeXnn3/GSy+9BC8vL9ja2qJp06ZYsGABSkpKNOJCQ0PRsmVLyOVy9OjRA/b29mjYsCGWLVtWZp83b95EWFgYHBwc4OHhgffffx8FBQVPzeVpnn/+ecyePRs3btzAt99+CwD45ZdfIBKJNO7O/vTTTxCJRPjPf/6jsb1MJsOQIUOqnAeRMbGIJTIBhUKB9PR03Lt3DxcvXsR7772HnJwcjTt55Tl9+jROnDiB119/HWvWrMG7776LQ4cOITQ0FHl5eWXiJ06ciPj4eMydOxfvvfcefv31V0yYMEEjZs6cOZg9ezbatGmD5cuXo0mTJnjxxReRm5ur8/s5efIkrl69ijfeeAM2Njb4z3/+o1OTglWrVsHb2xsBAQHYtm0btm3bhpkzZ5Z5D1euXMG8efMwYMAAbNy4EbNnz8bLL7+MkpISLFq0CF27dsXy5cuxbds2jW1Xr16NoKAgzJ8/H4sWLYK1tTUGDx6M3377rUwux44dw7hx4/D6669j2bJlyM/Px6BBg5CRkaHzcXjctWvXAABubm4Vxly8eBHdunVDfHw8PvzwQ8yePRvXr19HaGgoTp48CQDo3r07wsPDAQAff/yx+jjJZDIAQGRkJGQyGSIjI5+aU8eOHfHee+9h+fLlOH/+PN555x34+flh7ty56hgPDw/Y2dnh119/xf379/V+32fOnEFhYSGCg4MrjJk5cyaKi4uxZMkSrfu6e/cuOnbsiIMHD2LChAlYvXo1mjVrhjFjxmDVqlUASr+AdOnSBUePHlVvd+7cOXVRf/z4cfXy2NhYBAUFabTTdXZ2RtOmTTXiKrJlyxbUrVsXU6ZMwerVq/Hss89izpw5mD59epnYBw8eoE+fPmjTpg1WrlyJgIAAfPTRRxpt3x8+fIiePXti3759mDBhAmbOnInY2Fh8+OGHT81FF//9738BQH1HvWvXrhCJRBrHKjY2FlZWVjh27Jh62b1795CYmIju3bsbJA8ioxGIqNpEREQIAMo8bG1thS1btpSJByDMnTtX/TwvL69MzJ9//ikAELZu3VrmdXr16iUolUr18vfff18Qi8VCZmamIAiCkJaWJtjY2AgvvfSSRtzHH38sABBGjhyp0/uaMGGC4OPjo97H/v37BQBCXFxcue//+vXr6mUtWrQQQkJCyuxTFdu7d2+N3Dp16iSIRCLh3XffVS8rLi4WvL29y+znyeNVWFgotGzZUnj++ec1lgMQbGxshKtXr6qXxcfHCwCEtWvXan3v169fFwAI8+bNE+7duyfcuXNHiImJEYKCggQAwk8//aTxOo+fz7CwMMHGxka4du2aetmtW7cER0dHoXv37uplP/zwgwBAiI6OrvA4RUREaM1TRaFQCF5eXkK9evUEAMLevXvLxMyZM0cAIDg4OAh9+/YVPv30U+HMmTMVvvfly5erl23atEkAIJw/f75MfKNGjYSXXnpJEARBGD16tCCRSIRbt24JgiAI0dHRAgDhhx9+UMePGTNG8PT0FNLT0zX28/rrrwvOzs7q87t8+XJBLBYLWVlZgiAIwpo1a4RGjRoJ7du3Fz766CNBEAShpKREcHFxEd5///0yeb344ouCTCbTfuCE8v/+3nnnHcHe3l7Iz89XLwsJCSnzN1lQUCA0aNBAGDRokHrZqlWrBADCrl271Mtyc3OFZs2aVXi+H6c696dPn64wxtnZWQgKClI/b9GihfDaa6+pnwcHBwuDBw8WAAgJCQmCIAjC7t27BQBCfHy81tcnMjXeiSUygS+//BIHDhzAgQMH8O2336JHjx546623sHv3bq3bPd6uraioCBkZGWjWrBlcXFzwzz//lIl/++23IRKJ1M+7deuGkpIS3LhxAwBw8OBBFBYWYuLEiRpxkydP1vm9FBcX4/vvv8eQIUPU+3j++efh4eGhVweviowZM0Yjtw4dOkAQBIwZM0a9TCwWo127dvj33381tn38eD148AAKhQLdunUr91j16tULTZs2VT9v3bo1nJycyuyzInPnzoW7uzsaNGiA0NBQXLt2DUuXLi3zM61KSUkJ9u/fj7CwMI22mJ6enhg6dCiOHTuGrKysp77uqFGjIAiCzsOhOTk5YdWqVbh//z6GDBmC3r17l4mZN28eduzYgaCgIOzbtw8zZ87Es88+i+DgYCQkJGjdv+rOtaurq9a4WbNmab0bKwgCfvrpJ7z88ssQBAHp6enqR+/evaFQKNTnUfW5PnHiBIDSu4vdunVDt27dEBsbCwC4cOECMjMz0a1btzKv5erqivT0dK35Apqfp+zsbKSnp6Nbt27Iy8tDYmKiRmzdunU1flmxsbFB+/btNT5Pv//+Ozw9PfHqq6+ql9nb21fYIa4y6tatqzFKwePHJDs7G/Hx8Xj77bchlUrVy2NjY+Hi4oKWLVsaLA8iY2ARS2QC7du3R69evdCrVy8MGzYMv/32GwIDAzFhwgQUFhZWuN3Dhw8xZ84c+Pj4wNbWFlKpFO7u7sjMzCy3TaSvr6/Gc1Vh8eDBAwBQF7PNmzfXiHN3d9coQkpKSnDnzh2NhyrP/fv34969e2jfvj2uXr2Kq1ev4vr16+jRowe+++47KJXKShyhit+Ds7MzAMDHx6fMctX7UtmzZw86duwIiUSCevXqwd3dHevWrdPpWAGlx+vJfVbk7bffxoEDB3Do0CGcOXMGaWlpWn8WvnfvHvLy8uDv719mnUwmg1KpREpKik6vra/nnnsOADQ6Az3pjTfeQGxsLB48eID9+/dj6NChiIuLw8svv4z8/PynvobwRPvkJzVp0gT//e9/sXHjRty+fbvM+nv37iEzMxMbN26Eu7u7xmP06NEAHnUyCw4Ohr29vUYR1q1bN3Tv3h1///038vPz1eu6du1abq6Pf1GqyMWLFzFw4EA4OzvDyckJ7u7u6kL1yc+Ut7d3mX0++Xm6ceMGmjVrViauvM9EZeXk5MDR0VH9vFu3brh9+zauXr2KEydOQCQSoVOnThrFbWxsLLp06QIrK5YIZN44OgGRGbCyskKPHj2wevVqXLlyBS1atCg3buLEiYiIiMDkyZPRqVMnODs7QyQS4fXXXy+3WBSLxeXu52kFxpNSUlLQuHFjjWXR0dEIDQ1V32197bXXyt32yJEj6NGjh16v97iK3kN5yx9/X7GxsRgwYAC6d++Or776Cp6enqhTpw4iIiLK7XRW1WPVvHnzCjszWTInJye88MILeOGFF1CnTh188803OHnyJEJCQsqNV7UBfvDgAby9vbXue+bMmdi2bRuWLl2KsLAwjXWqz/Pw4cMxcuTIcrdXdUarU6cOOnTogKNHj+Lq1au4c+cOunXrhvr166OoqAgnT55EbGwsAgIC4O7uXmY/Dx48gFQq1ZprZmYmQkJC4OTkhPnz56Np06aQSCT4559/8NFHH5X5+zPU315V3Lx5EwqFAs2aNVMvUxXxR48exb///ovg4GA4ODigW7duWLNmDXJychAXF4dPP/202vIkqiwWsURmori4GAC0znD0448/YuTIkVi5cqV6WX5+vkYPbH00atQIQOkYr4//pH3v3j2NO0YNGjTAgQMHNLZt06YNcnNz8fPPP2PIkCEaP4mqhIeHY/v27VqLWF3ugFXGTz/9BIlEgn379sHW1la9PCIiwiivpy93d3fY29vj0qVLZdYlJibCyspKfbfZWMdIX+3atcM333xT7p1TlYCAAAClozK0atVK6/6aNm2K4cOHY8OGDWVmznJ3d4ejoyNKSkp0+nLQrVs3LF26FAcPHoRUKkVAQABEIhFatGiB2NhYxMbGVjh83fXr19GmTRut+4+JiUFGRgZ2796t0eHpaaNPaNOoUSNcuHChzJ3g8j4TlaHq6Ph4kxFfX1/4+voiNjYW//77r7p5Rffu3TFlyhT88MMPKCkpYacusgj8rYDIDBQVFWH//v2wsbFR9zovj1gsLnMnZ+3atWWG+NFVr169UKdOHaxdu1Zjv6qe3yoSiUTd/EH1cHV1RWRkJHJzczF+/Hi8+uqrZR79+/fHTz/9pHXIIAcHh0oX4dqIxWKIRCKNY5OUlISoqCiDv1ZliMVivPjii/j55581hki7e/cuduzYga5du8LJyQlA6TECUO5x0nWILV3l5eXhzz//LHedqme9tp+7n332WdjY2ODvv//W6fVmzZqFoqKiMsNPicViDBo0CD/99BMuXLhQZrsnh4rr1q0bCgoKsGrVKnUvfNXybdu24datW+W2h1UoFLh27Ro6d+6sNU/VndXH/04KCwvx1Vdf6fQ+y9OvXz/cunVLY9i3vLw8bNy4sdL7VDl8+DAWLFiAxo0bY9iwYRrrunXrhsOHD+PUqVPqY9K2bVs4OjpiyZIl6uHoiMwd78QSmcAff/yh7giSlpaGHTt24MqVK5g+fbq6cClP//79sW3bNjg7OyMwMBB//vknDh48qHUYJ23c3d0xbdo0LF68GP3790e/fv0QFxeHP/7446k/rwLA9u3b4ebmVmEBMGDAAHz99df47bffKuzg9Oyzz2LdunVYuHAhmjVrBg8PD/UYq1Xx0ksv4bPPPkOfPn0wdOhQpKWl4csvv0SzZs3MZhajhQsX4sCBA+jatSvGjRsHa2trbNiwAQUFBRpFXdu2bSEWi7F06VIoFArY2tqqO89FRkZi9OjRiIiI0LlzlzZ5eXno3LkzOnbsiD59+sDHxweZmZmIiopCbGwswsLCEBQUVOH2EokEL774Ig4ePIj58+c/9fVUd2O/+eabMuuWLFmC6OhodOjQAWPHjkVgYCDu37+Pf/75BwcPHtQYAqxTp06wtrYuM1NY9+7d1dMgl1fEHjx4EIIg4JVXXtGaZ+fOneHq6oqRI0ciPDwcIpEI27Ztq1LzgLFjx+KLL77AiBEjcObMGXh6emLbtm2wt7fXaz+q60lxcTHu3r2Lw4cP48CBA2jUqBF++eUXSCQSjfhu3bph+/btEIlE6uYFYrEYnTt3xr59+xAaGgobG5tKvy+i6sIilsgE5syZo/63RCJBQEAA1q1bh3feeUfrdqtXr4ZYLMb27duRn5+PLl264ODBg+X2MNfVwoULIZFIsH79enXBsH//frz00ktat0tLS8PBgwfxxhtvVNj+r2fPnrC3t8e3335bYRE7Z84c3LhxA8uWLUN2djZCQkIMUsQ+//zz+N///oclS5Zg8uTJaNy4MZYuXYqkpCSzKWJVP3XPmDEDixcvhlKpRIcOHfDtt99q/LzeoEEDrF+/HosXL8aYMWNQUlKC6Ojop048UBkuLi7qLx4RERG4c+cOxGIx/P39sXz5cvWYtcCju5JPnv8333wTgwYNQkpKSpkOeOWZNWsWvv322zK/KNSvXx+nTp3C/PnzsXv3bnz11Vdwc3NDixYtsHTpUo1YBwcHBAUF4fTp0xqdt1SFq4+Pj7r5zON++OEHdO3aVWNkivK4ublhz549mDp1KmbNmgVXV1cMHz4cPXv2rPTfn729PQ4dOoSJEydi7dq1sLe3x7Bhw9C3b1/06dNH5/2oric2NjaoV68eWrVqhVWrVmH06NEanbpUVMckICBA4wtwt27dsG/fvnKLfSJzJBKqs5U5ERHVGOfOnUObNm2wadMmjSHPSkpKEBgYiNdeew0LFiwwYYba3blzB40bN8bOnTufeieWiMwP28QSEVGlnD59GgAQGBiosVwsFmP+/Pn48ssvtXZUNLVVq1ahVatWLGCJLBTvxBIRkV7+/PNPREdHY9myZWjQoAHkcjnHFCWiasc2sUREpJcNGzbghx9+QLdu3bB27VoWsERkErwTS0REREQWh1+fiYiIiMjisIglIiIiIotTq9rEKpVK3Lp1C46OjmYzjSMRERERPSIIArKzs+Hl5aW1zX2tKmJv3bql08DbRERERGRaKSkp8Pb2rnB9rSpiVTOXpKSkaJ3ak4iIiIhMIysrCz4+PuXOOPe4WlXEqpoQODk5sYglIiIiMmNPa/rJjl1EREREZHFYxBIRERGRxWERS0REREQWp1a1iSUiIjIkQRBQXFyMkpISU6dCZDHEYjGsra2rPNwpi1giIqJKKCwsxO3bt5GXl2fqVIgsjr29PTw9PWFjY1PpfbCIJSIi0pNSqcT169chFovh5eUFGxsbTqJDpANBEFBYWIh79+7h+vXraN68udYJDbRhEUtERKSnwsJCKJVK+Pj4wN7e3tTpEFkUOzs71KlTBzdu3EBhYSEkEkml9sOOXURERJVU2TtIRLWdIf52+NdHRERERBaHRSwRERERWRwWsURERGSxQkNDMXnyZPXzvLw8DBo0CE5OThCJRMjMzCx3GVk+FrFERES1xKhRoyASifDuu++WWTd+/HiIRCKMGjWq+hN7wpYtWyASiSASiSAWi+Hq6ooOHTpg/vz5UCgUGrG7d+/GggUL1M+/+eYbxMbG4sSJE7h9+zacnZ3LXVYbff311+jWrRtcXV3h6uqKXr164dSpU1q3uX37NoYOHYpnnnkGVlZWGl8YVB4/X6pHZTtr6YNFLBERUS3i4+ODnTt34uHDh+pl+fn52LFjB3x9fU2YmSYnJyfcvn0bN2/exIkTJ/D2229j69ataNu2LW7duqWOq1evHhwdHdXPr127BplMhpYtW6JBgwYQiUTlLtNXSUkJlEqlQd6bqcTExOCNN95AdHQ0/vzzT/j4+ODFF19EampqhdsUFBTA3d0ds2bNQps2bSqMU50v1ePGjRvGeAsaWMQSEZmYUqlEUlISzp8/j6SkJIv/j7K2y82t+JGfr3vsYzVmhbGVERwcDB8fH+zevVu9bPfu3fD19UVQUJBGrFKpxOLFi9G4cWPY2dmhTZs2+PHHH9XrS0pKMGbMGPV6f39/rF69WmMfo0aNQlhYGFasWAFPT0+4ublh/PjxKCoq0pqnSCRCgwYN4OnpCZlMhjFjxuDEiRPIycnBhx9+qI57vDlBaGgoVq5ciaNHj0IkEiE0NLTcZUBpcTZt2jQ0bNgQDg4O6NChA2JiYtT73bJlC1xcXPDLL78gMDAQtra2SE5O1nm7ffv2QSaToW7duujTpw9u376t8f42b96MFi1awNbWFp6enpgwYYJ6XWZmJt566y24u7vDyckJzz//POLj47UeL11s374d48aNQ9u2bREQEIBNmzZBqVTi0KFDFW7j5+eH1atXY8SIEVrvYKvOl+pRv379Kuf7NBwnlojIhBISEhAZGYnExETk5+dDIpEgICAAAwcOhEwmM3V6VAl161a8rl8/4LffHj338AAqmvArJAR4rDaCnx+Qnq4ZIwiVy/HNN99EREQEhg0bBqC0oBo9erRGMQYAixcvxrfffov169ejefPmOHr0KIYPHw53d3eEhIRAqVTC29sbP/zwA9zc3NR3TD09PfHaa6+p9xMdHQ1PT09ER0fj6tWrGDJkCNq2bYuxY8fqlbeHhweGDRuGzZs3o6SkBGKxWGP97t27MX36dFy4cAG7d+9WzwZV3rIJEyZALpdj586d8PLyQmRkJPr06YPz58+jefPmAErb1y5duhSbNm2Cm5sbPDw8dN5uxYoV2LZtG6ysrDB8+HBMmzYN27dvBwCsW7cOU6ZMwZIlS9C3b18oFAocP35c/T4GDx4MOzs7/PHHH3B2dsaGDRvQs2dPXL58GfXq1UNSUhIaN26M6OhodVFeGXl5eSgqKkK9evUqvQ+VnJwcNGrUCEqlEsHBwVi0aBFatGhR5f1qwyKWiMhEEhISsGbNGqSnp8PHxwcODg7Izc1FXFwcUlJSEB4ezkKWjGL48OGYMWOG+iff48ePY+fOnRpFbEFBARYtWoSDBw+iU6dOAIAmTZrg2LFj2LBhA0JCQlCnTh3MmzdPvU3jxo3x559/YteuXRpFrKurK7744guIxWIEBATgpZdewqFDh/QuYgEgICAA2dnZyMjIgIeHh8a6evXqwd7eHjY2NmjQoIF6+ZPLkpOTERERgeTkZHh5eQEApk2bhr179yIiIgKLFi0CABQVFeGrr75S/4yuz3br169H06ZNAZQWzPPnz1fns3DhQkydOhWTJk1SL3vuuecAAMeOHcOpU6eQlpYGW1tbAMCKFSsQFRWFH3/8EW+//Tbq1KkDf3//Kk+08dFHH8HLywu9evWq0n78/f2xefNmtG7dGgqFAitWrEDnzp1x8eJFeHt7V2nf2rCIJSIyAaVSicjISKSnpyMwMFDdRs/JyQmBgYGQy+WIioqCv78/B9S3MDk5Fa974sYh0tIqjn3ytCclVTqlMtzd3fHSSy9hy5YtEAQBL730EqRSqUbM1atXkZeXhxdeeEFjeWFhoUazgy+//BKbN29GcnIyHj58iMLCQrRt21ZjmxYtWmjcNfX09MT58+crlbvw/7efqzLN7/nz51FSUoJnnnlGY3lBQQHc3NzUz21sbNC6dWu9t7O3t1cXsEDp+037/5OdlpaGW7duoWfPnuXmFh8fj5ycHI39AcDDhw9x7do1AEDDhg2RmJhY4ftLTk5GYGCg+vnHH3+Mjz/+WCNmyZIl6i8uVe2E1alTJ/UXHQDo3LkzZDIZNmzYoNHpztBYxBIRmUBycjISExPh4+NT5j9jkUgEb29vJCQkIDk5GX5+fqZJkirFwcH0sbp488031e0wv/zyyzLrc/6/Gv/tt9/QsGFDjXWqO4Q7d+7EtGnTsHLlSnTq1AmOjo5Yvnw5Tp48qRFfp04djecikajSbb8TEhLg5ORUpsjTR05ODsRiMc6cOVOmSULdx9qD2NnZafx96rpdee9XVXzb2dk9NTdPT88yTTsAwMXFReu2Kl5eXjh79qz6+ZPNBVasWIElS5bg4MGDGkW6odSpUwdBQUG4evWqwff9OBaxREQmkJ2djfz8fDhUUJk4ODggNTUV2dnZ1ZwZ1RZ9+vRBYWEhRCIRevfuXWb9452ZQkJCyt3H8ePH0blzZ4wbN069THW30BjS0tKwY8cOhIWFVekXiqCgIJSUlCAtLQ3dunUz+naPc3R0hJ+fHw4dOoQePXqUWR8cHIw7d+7A2tq60l9gra2t0axZs3LXLVu2DJ9++in27duHdu3aVWr/T1NSUoLz58+jX79+Rtm/CotYIiITcHR0hEQiQW5uLpycnMqsz83NhUQi0Rg6iMiQxGIxEhIS1P9+kqOjI6ZNm4b3338fSqUSXbt2VXdAcnJywsiRI9G8eXNs3boV+/btQ+PGjbFt2zacPn0ajRs3rnJ+giDgzp07EAQBmZmZ+PPPP7Fo0SI4OztjyZIlVdr3M888g2HDhmHEiBFYuXIlgoKCcO/ePRw6dAitW7fGSy+9ZNDtnvTJJ5/g3XffhYeHB/r27Yvs7GwcP34cEydORK9evdCpUyeEhYVh2bJleOaZZ3Dr1i389ttvGDhwINq1a4fU1FT07NkTW7duRfv27XV+30uXLsWcOXOwY8cO+Pn54c6dOwBK7yKr7iTPmDEDqamp2Lp1q3o71V3dnJwc3Lt3D2fPnoWNjY26ycL8+fPRsWNHNGvWDJmZmVi+fDlu3LiBt956S+fcKoNFLBGRCfj6+iIgIABxcXEabWKB0v+8b968ieDgYLMat5NqnvK+QD1uwYIFcHd3x+LFi/Hvv//CxcUFwcHB6vaV77zzDuLi4jBkyBCIRCK88cYbGDduHP74448q55aVlQVPT0+IRCI4OTnB398fI0eOxKRJk56aty4iIiLUHaxSU1MhlUrRsWNH9O/f3yjbPW7kyJHIz8/H559/jmnTpkEqleLVV18FUNr04Pfff8fMmTMxevRo3Lt3Dw0aNED37t3Vw1YVFRXh0qVLyKtoaIsKrFu3DoWFherXUpk7dy4++eQTAKWTGyQnJ2usf7wN9JkzZ7Bjxw40atQISf/fUPvBgwcYO3Ys7ty5A1dXVzz77LM4ceKERrtcYxAJQmUH6LA8WVlZcHZ2hkKhMMgfABFRVTw+OoG3t7d6dIKbN29CKpVydAIzlp+fj+vXr6Nx48bVMjMRUU2j7W9I13qNXV6JiExEJpMhPDwcQUFByMjIwOXLl5GRkYHg4GAWsERET8HmBEREJiSTyeDv74/k5GRkZ2fD0dERvr6+HFaLiOgpWMQSEZmYlZUVh9EiItITv+oTERERkcVhEUtEREREFodFLBERERFZHBaxRERERGRxWMQSERERkcVhEUtEREREFodFLBEREVm0Tz75BG3btq3SPpKSkiASiXD27FmD5ETGxyKWiIiolggNDcXkyZPLLN+yZQtcXFyqPR9dPa3AnDZtGg4dOlSl1/Dx8cHt27fRsmVLAEBMTAxEIhEyMzP13tfRo0fx8ssvw8vLCyKRCFFRUU/d5vbt2xg6dCieeeYZWFlZlXueioqKMH/+fDRt2hQSiQRt2rTB3r179c6vpmARS0RERGahpKQESqVS7+3q1q0LNze3Kr22WCxGgwYNYG1d9XmgcnNz0aZNG3z55Zc6b1NQUAB3d3fMmjULbdq0KTdm1qxZ2LBhA9auXQu5XI53330XAwcORFxcXJVztkQWU8QuXrwYzz33HBwdHeHh4YGwsDBcunTJ1GkRERFpys2t+JGfr3vsw4dPjzWSUaNGISwsDPPmzYO7uzucnJzw7rvvorCwUB0TGhqKCRMmYMKECXB2doZUKsXs2bMhCII6pqCgANOmTUPDhg3h4OCADh06ICYmRr1edQf4l19+QWBgIGxtbZGcnKx3vk82J1Dlv2jRItSvXx8uLi6YP38+iouL8cEHH6BevXrw9vZGRESEepvH7/YmJSWhR48eAABXV1eIRCKMGjVK53z69u2LhQsXYuDAgTpv4+fnh9WrV2PEiBFwdnYuN2bbtm34+OOP0a9fPzRp0gTvvfce+vXrh5UrV+r8OjWJxRSxR44cwfjx4/HXX3/hwIEDKCoqwosvvohcI/4RExER6a1u3YofgwZpxnp4VBzbt69mrJ9f2RgjOnToEBISEhATE4PvvvsOu3fvxrx58zRivvnmG1hbW+PUqVNYvXo1PvvsM2zatEm9fsKECfjzzz+xc+dOnDt3DoMHD0afPn1w5coVdUxeXh6WLl2KTZs24eLFi/Dw8DBI/ocPH8atW7dw9OhRfPbZZ5g7dy769+8PV1dXnDx5Eu+++y7eeecd3Lx5s8y2Pj4++OmnnwAAly5dwu3bt7F69WoApYW3SCQySI76KigogEQi0VhmZ2eHY8eOmSQfU6v6PfNq8mSbjy1btsDDwwNnzpxB9+7dy92moKAABQUF6udZWVlGzZGIiKimsLGxwebNm2Fvb48WLVpg/vz5+OCDD7BgwQJYWZXeA/Px8cHnn38OkUgEf39/nD9/Hp9//jnGjh2L5ORkREREIDk5GV5eXgBK267u3bsXERERWLRoEYDSdp5fffVVhT+hV1a9evWwZs0aWFlZwd/fH8uWLUNeXh4+/vhjAMCMGTOwZMkSHDt2DK+//rrGtmKxGPXq1QMAeHh4aLQXdnZ2hr+/v0Fz1VXv3r3x2WefoXv37mjatCkOHTqE3bt3o6SkxCT5mJrFFLFPUigUAKD+kJVn8eLFZb41EhERGVVOTsXrxGLN52lpFcdaPfFjaVJSpVOqjDZt2sDe3l79vFOnTsjJyUFKSgoaNWoEAOjYsaPGXclOnTph5cqVKCkpwfnz51FSUoJnnnlGY78FBQUa7VdtbGzQunVrg+ffokULdbENAPXr11d32gJKC1U3NzekaTsH5Rg4cKBezQQMafXq1Rg7diwCAgIgEonQtGlTjB49Gps3bzZJPqZmkUWsUqnE5MmT0aVLF40P5JNmzJiBKVOmqJ9nZWXBx8enOlIkIqLaysHB9LEVcHJyUt8EelxmZmaF7TArKycnB2KxGGfOnIH4ieK97mNNIezs7Izy83ydOnU0notEonKXVaYjmam4u7sjKioK+fn5yMjIgJeXF6ZPn44mTZqYOjWTsMgidvz48bhw4cJT24DY2trC1ta2mrIiIiIyb/7+/ti/f3+Z5f/880+ZO6bx8fF4+PAh7OzsAAB//fUX6tatq3Ez6OTJkxrb/PXXX2jevDnEYjGCgoJQUlKCtLQ0dOvWzQjvxrhsbGwAwCx/qpdIJGjYsCGKiorw008/4bXXXjN1SiZhMR27VCZMmIA9e/YgOjoa3t7epk6HiIjIYrz33nu4fPkywsPDce7cOVy6dAmfffYZvvvuO0ydOlUjtrCwEGPGjIFcLsfvv/+OuXPnYsKECRo/0ScnJ2PKlCm4dOkSvvvuO6xduxaTJk0CADzzzDMYNmwYRowYgd27d+P69es4deoUFi9ejN9++61S+V+6dAlnz57VeBQVFVX+gGjRqFEjiEQi7NmzB/fu3UPO/zcTiYyMREBAgNZtc3Jy1PkBwPXr13H27FmNkRdmzJiBESNGaGyn2iYnJwf37t3D2bNnIZfL1etPnjyJ3bt3499//0VsbCz69OkDpVKJDz/80EDv2rJYzJ1YQRAwceJEREZGIiYmBo0bNzZ1SkRERBalSZMmOHr0KGbOnIlevXqhsLAQAQEB+OGHH9CnTx+N2J49e6J58+bo3r07CgoK8MYbb+CTTz7RiBkxYgQePnyI9u3bQywWY9KkSXj77bfV6yMiIrBw4UJMnToVqampkEql6NixI/r371+p/J/sgAUAKSkpldrX0zRs2BDz5s3D9OnTMXr0aIwYMQJbtmyBQqF46hCff//9t3qILgDqpo0jR47Eli1bAJRObvDkcGJBQUHqf585cwY7duxAo0aNkPT/7aHz8/Mxa9Ys/Pvvv6hbty769euHbdu2mfVEFcYkEh4f0M2MjRs3Djt27MDPP/+s0SvQ2dlZ/VPH02RlZcHZ2RkKhQJOTk7GSpWIiGq4/Px8XL9+HY0bNy4z5FFNMGrUKGRmZmqdaSo0NBRt27bFqlWrqi0vqjm0/Q3pWq9ZTHOCdevWQaFQIDQ0FJ6enurH999/b+rUiIiIiKiaWVRzAiIiIiIiwIKKWCIiIqoeqnab2jw+fSyRKVhMcwIiIiIiIhUWsURERERkcVjEEhEREZHFYRFLRERERBaHRSwRERERWRwWsURERERkcVjEEhERkUUTiURaZxfTxahRoxAWFmaQfKh6sIglIiKqJSy1UHta3rdv30bfvn2r9BqrV6/WGB83NDQUkydPrtI+VZKSkiASico8/vrrL522z8jIgLe3N0QiETIzMzXWffnll5DJZLCzs4O/vz+2bt1qkJwtASc7ICIiIrNQWFgIGxsbvbdr0KBBlV/b2dm5yvt4moMHD6JFixbq525ubjptN2bMGLRu3Rqpqakay9etW4cZM2bg66+/xnPPPYdTp05h7NixcHV1xcsvv2zQ3M0R78QSEREZUm5uxY/8fN1jHz58eqyBHTlyBO3bt4etrS08PT0xffp0FBcXAwD27NkDFxcXlJSUAADOnj0LkUiE6dOnq7d/6623MHz4cPXzY8eOoVu3brCzs4OPjw/Cw8OR+1jefn5+WLBgAUaMGAEnJye8/fbblcr78eYEqrueu3btUr/2c889h8uXL+P06dNo164d6tati759++LevXvqfTx+t3fUqFE4cuQIVq9erb5rmpSUVKncHufm5oYGDRqoH3Xq1HnqNuvWrUNmZiamTZtWZt22bdvwzjvvYMiQIWjSpAlef/11vP3221i6dGmVc7UELGKJiIgMqW7dih+DBmnGenhUHPvkz+N+fmVjDCg1NRX9+vXDc889h/j4eKxbtw7/+9//sHDhQgBAt27dkJ2djbi4OAClBa9UKtWYfvbIkSMIDQ0FAFy7dg19+vTBoEGDcO7cOXz//fc4duwYJkyYoPG6K1asQJs2bRAXF4fZs2cb7P3MnTsXs2bNwj///ANra2sMHToUH374IVavXo3Y2FhcvXoVc+bMKXfb1atXo1OnThg7dixu376N27dvw8fHB0Bp4f3JJ59UKqcBAwbAw8MDXbt2xS+//PLUeLlcjvnz52Pr1q2wsipbshUUFEAikWgss7Ozw6lTp1BUVFSpHC0Ji1giIiLCV199BR8fH3zxxRcICAhAWFgY5s2bh5UrV0KpVMLZ2Rlt27ZVF60xMTF4//33ERcXh5ycHKSmpuLq1asICQkBACxevBjDhg3D5MmT0bx5c3Tu3Blr1qzB1q1bkf/YHennn38eU6dORdOmTdG0aVODvZ9p06ahd+/ekMlkmDRpEs6cOYPZs2ejS5cuCAoKwpgxYxAdHV3uts7OzrCxsYG9vb36rqlYLAYANG3aFFKpVK9c6tati5UrV+KHH37Ab7/9hq5duyIsLExrIVtQUIA33ngDy5cvh6+vb7kxvXv3xqZNm3DmzBkIgoC///4bmzZtQlFREdLT0/XK0RKxTSwREZEh5eRUvO7/CyG1tLSKY5+882aAn7O1SUhIQKdOnSASidTLunTpgpycHNy8eRO+vr4ICQlBTEwMpk6ditjYWCxevBi7du3CsWPHcP/+fXh5eaF58+YAgPj4eJw7dw7bt29X708QBCiVSly/fh0ymQwA0K5dO6O8n9atW6v/Xb9+fQBAq1atNJalaTv+FTh06JDW9S1atMCNGzcAlN69/uOPPyCVSjFlyhR1zHPPPYdbt25h+fLlGDBgQLn7mTFjBmQymUbzjCfNnj0bd+7cQceOHSEIAurXr4+RI0di2bJl5d65rWlYxBIRERmSg4PpY40kNDQUmzdvRnx8POrUqYOAgACEhoYiJiYGDx48UN+FBYCcnBy88847CA8PL7Ofx+8sOhjpfT3e3lRVmD+5TKlUGvx1f//9d/VP+XZ2dhXGdejQAQcOHKhw/eHDh3H+/Hn8+OOPAEq/AACAVCrFzJkzMW/ePNjZ2WHz5s3YsGED7t69C09PT2zcuBGOjo5wd3c34LsyTyxiiYiICDKZDD/99BMEQVAXfcePH4ejoyO8vb0BPGoX+/nnn6sL1tDQUCxZsgQPHjzA1KlT1fsLDg6GXC5Hs2bNqv/NGICNjY26E5s+GjVqpFPc2bNn4enpWeH6n376CQ8f69x3+vRpvPnmm4iNjS3T7KJOnTrqc7Rz507079+fd2KJiIioZlEoFDh79qzGMjc3N4wbNw6rVq3CxIkTMWHCBFy6dAlz587FlClT1AWRq6srWrduje3bt+OLL74AAHTv3h2vvfYaioqKNO7EfvTRR+jYsSMmTJiAt956Cw4ODpDL5Thw4IB6W0PkrepwZWh+fn44efIkkpKSULduXdSrVw9WVlbo2bMnBg4cWKaDmjbffPMNbGxsEBQUBADYvXs3Nm/ejE2bNqljIiMjMWPGDCQmJgJAmUJV1cZVJpPBxcUFAHD58mWcOnUKHTp0wIMHD/DZZ5/hwoUL+Oabb6ry1i0Gi1giIqJaJCYmRl1MqYwZMwabNm3C77//jg8++ABt2rRBvXr1MGbMGMyaNUsjNiQkBGfPnlWPQlCvXj0EBgbi7t278Pf3V8e1bt0aR44cwcyZM9GtWzcIgoCmTZtiyJAhBs/bGKZNm4aRI0ciMDAQDx8+xPXr1+Hn54dr165VqtPUggULcOPGDVhbWyMgIADff/89Xn31VfV6hUKBS5cu6bXPkpISrFy5EpcuXUKdOnXQo0cPnDhxAn5+fnrnZ4lEgqqRRS2QlZUFZ2dnKBQKODk5mTodIiKyUPn5+bh+/ToaN25cZogjIno6bX9DutZrNb/BBBERERHVOCxiiYiIiMjisIglIiIiIovDIpaIiIiILA6LWCIiokqqRX2jiQzKEH87LGKJiIj0pJr5KS8vz8SZEFkm1d/O47Oo6YvjxBIREelJLBbDxcUFaWlpAAB7e3v1LFdEVDFBEJCXl4e0tDS4uLhALBZXel8sYomIiCqhQYMGAKAuZIlIdy4uLuq/ocpiEUtERFQJIpEInp6e8PDwQFFRkanTIbIYderUqdIdWBUWsURERFUgFosN8h8yEemHHbuIiIiIyOKwiCUiIiIii1M7mxPk5gLl/fQjFgMSiWZcRaysADu7ysXm5QEVjY8mEgH29pWLffgQUCorzsPBoXKx+flASYlhYu3tS/MGgIICoLjYMLF2dqXHGQAKCwFt7dP0iZVIHn1W9IktKiqNr4itLWBtrX9scXHpsaiIjQ2gGq5En9iSktJzV5E6dUrj9Y1VKks/a4aItbYuPRZA6d+EtqGN9InV5++e14jyY3mN0D+W14jSf/MaUbnYmn6N0HYsHifUIgqFQgAgKEpPZ9lHv36aG9jblx8HCEJIiGasVFpxbLt2mrGNGlUcGxioGRsYWHFso0aase3aVRwrlWrGhoRUHGtvrxnbr1/FsU9+hF59VXtsTs6j2JEjtcempT2KHTdOe+z1649ip03THnvhwqPYuXO1x5469Sh22TLtsdHRj2K/+EJ77J49j2IjIrTH7tr1KHbXLu2xERGPYvfs0R77xRePYqOjtccuW/Yo9tQp7bFz5z6KvXBBe+y0aY9ir1/XHjtu3KPYtDTtsSNHPorNydEe++qrggZtsbxGlD54jXj04DWi9MFrROmD14jSRxWvEQpAACAoFApBGzYnICIiIiKLIxIEQTB1EtUlKysLzs7OUNy6BScnp7IB/Bmg/Fj+VKh/LH8qLP03fyqsXCyvEaX/5jVC/1heI0r/zWtE5WLN5BqR9eABnL28oFAoyq/XVOnXyiL2KQeFiIiIiExD13qNzQmIiIiIyOLUztEJiIioWiiVSiQnJyM7OxuOjo7w9fWFlRXvnxBR1bGIJSIio0hISEBkZCQSExORn58PiUSCgIAADBw4EDKZzNTpEZGFYxFLREQGl5CQgDVr1iA9PR0+Pj5wcHBAbm4u4uLikJKSgvDwcBayRFQl/E2HiIgMSqlUIjIyEunp6QgMDISTkxPEYjGcnJwQGBiI9PR0REVFQamtZzMR0VOwiCUiIoNKTk5GYmIifHx8IFINgfX/RCIRvL29kZCQgOTkZBNlSEQ1AYtYIiIyqOzsbOTn58Ph8bEfH+Pg4ID8/HxkZ2dXc2ZEVJOwiCUiIoNydHSERCJBbgWDt+fm5kIikcDR0bGaMyOimoRFLBERGZSvry8CAgKQkpKCJ+fTEQQBN2/ehEwmg6+vr4kyJKKagEUsEREZlJWVFQYOHAipVAq5XA6FQoHi4mIoFArI5XJIpVKEhYVxvFgiqhJOO0tEREZR3jixMpkMYWFhHF6LiCqka73GcWKJiMgoZDIZ/P39OWMXERkFi1giIjIaKysr+Pn5mToNIqqB+HWYiIiIiCwOi1giIiIisjgsYomIiIjI4rCIJSIiIiKLwyKWiIiIiCyO3kXs5s2bcf36dWPkQkRERESkE72L2MWLF6NZs2bw9fXFf//7X2zatAlXr141Rm5EREREROXSu4i9cuUKkpOTsXjxYtjb22PFihXw9/eHt7c3hg8fbowciYiIiIg0VGna2by8PMTGxuK7777D9u3bIQgCiouLDZmfQXHaWSLzoFQqOYsTERGVy2jTzu7fvx8xMTGIiYlBXFwcZDIZQkJC8OOPP6J79+5VSpqIar6EhARERkYiMTER+fn5kEgkCAgIwMCBAyGTyUydHhERWQi9i9g+ffrA3d0dU6dOxe+//w4XFxcjpEVENVFCQgLWrFmD9PR0+Pj4wMHBAbm5uYiLi0NKSgrCw8NZyBIRkU70/v3us88+Q5cuXbBs2TK0aNECQ4cOxcaNG3H58mVj5EdENYRSqURkZCTS09MRGBgIJycniMViODk5ITAwEOnp6YiKioJSqTR1qkREZAH0LmInT56M3bt3Iz09HXv37kXnzp2xd+9etGzZEt7e3sbIkYhqgOTkZCQmJsLHxwcikUhjnUgkgre3NxISEpCcnGyiDImIyJLo3ZwAAARBQFxcHGJiYhAdHY1jx45BqVTC3d3d0PkRUQ2RnZ2N/Px8ODg4lLvewcEBqampyM7OrubMyJyxEyARVUTvIvbll1/G8ePHkZWVhTZt2iA0NBRjx45F9+7d2T6WiCrk6OgIiUSC3Nzccnub5ubmQiKRwNHR0QTZkTliJ0Ai0kbvIjYgIADvvPMOunXrBmdnZ2PkREQ1kK+vLwICAhAXF4fAwECNJgWCIODmzZsIDg6Gr6+vCbMkc8FOgET0NHoXscuXLzdGHkRUw1lZWWHgwIFISUmBXC6Ht7e3ujC5efMmpFIpwsLC+FMxlekEqPrCo+oEKJfLERUVBX9/f35eiGoxvf76lUolNm/ejP79+6Nly5Zo1aoVBgwYgK1bt6IKcyYQUS0hk8kQHh6OoKAgZGRk4PLly8jIyEBwcDDvrJEaOwESkS50vhMrCAIGDBiA33//HW3atEGrVq0gCAISEhIwatQo7N69G1FRUUZMlYhqAplMBn9/f3bWoQqxEyAR6ULnInbLli04evQoDh06hB49emisO3z4MMLCwrB161aMGDHC4EkSUc1iZWUFPz8/U6dBZoqdAIlIFzrf+vjuu+/w8ccflylgAeD555/H9OnTsX37doMmR0REtY+qE2BKSkqZpmqqToAymYydAIlqOZ2L2HPnzqFPnz4Vru/bty/i4+MNkhQREdVeqk6AUqkUcrkcCoUCxcXFUCgUkMvl7ARIRAD0aE5w//591K9fv8L19evXx4MHDwySFFkuDkxORIag6gSoGic2NTUVEokEwcHBCAsLYydAItK9iC0pKYG1dcXhYrEYxcXFBkmKLBMHJiciQ2InQCLSRq/RCUaNGgVbW9ty1xcUFBgsKbI8HJiciIyBnQCJqCI6F7EjR458agxHJqidODA5ERERVTedi9iIiAhj5kEWTJ+ByXlHhYiIiAyBt8WoynQZmDw/P58DkxMREZHBsIilKnt8YPLycGByIiIiMjQWsVRlHJiciIiIqhuLWKoyDkxORERE1U0kPHnrrAbLysqCs7MzFApFufNxU9WUN06sTCbjwORERESkM13rNZ1GJ/jll190fuEBAwboHEs1CwcmJyIiouqiUxEbFham8VwkEmm0fXx8WKWSkhLDZEYWiQOTExERUXXQ6RaZUqlUP/bv34+2bdvijz/+QGZmJjIzM/H7778jODgYe/fuNXa+RERERES6T3agMnnyZKxfvx5du3ZVL+vduzfs7e3x9ttvIyEhwaAJEhERERE9Se/GiteuXYOLi0uZ5c7OzkhKSjJASkRERERE2uldxD733HOYMmUK7t69q1529+5dfPDBB2jfvr1Bk6OylEolkpKScP78eSQlJUGpVJo6JSIiIqJqp3dzgs2bN2PgwIHw9fWFj48PACAlJQXNmzdHVFSUofMr48svv8Ty5ctx584dtGnTBmvXrq01xXN5Q1gFBARg4MCBHMKKiIiIahW9i9hmzZrh3LlzOHDgABITEwGUDq3Uq1cvjVEKjOH777/HlClTsH79enTo0AGrVq1C7969cenSJXh4eBj1tU0tISEBa9asQXp6Onx8fODg4IDc3FzExcUhJSUF4eHhLGSJiIio1rCoyQ46dOiA5557Dl988QWA0p/WfXx8MHHiREyfPv2p21vqZAdKpRJLlixBXFwcAgMDNb4sCIIAuVyO4OBgfPTRRxyTlYiIiCyaQSc7eNKhQ4dw6NAhpKWllWmTuXnz5srs8qkKCwtx5swZzJgxQ73MysoKvXr1wp9//lnuNgUFBSgoKFA/z8rKMkpuxpacnIzExET4+PiUudstEong7e2NhIQEJCcnc4xWIiIiqhX0vm03b948vPjiizh06BDS09Px4MEDjYexpKeno6SkBPXr19dYXr9+fdy5c6fcbRYvXgxnZ2f1Q9WG19JkZ2cjPz8fDg4O5a53cHBAfn4+srOzqzkzIiIiItPQ+07s+vXrsWXLFvz3v/81Rj4GNWPGDEyZMkX9PCsryyILWUdHR0gkEuTm5pZ7Wz03NxcSiQSOjo4myI6IiIio+ul9J7awsBCdO3c2Ri5aSaVSiMVijaG9gNLhvRo0aFDuNra2tnByctJ4WCJfX18EBAQgJSUFTzZhFgQBN2/ehEwmg6+vr4kyJCIiIqpeehexb731Fnbs2GGMXLSysbHBs88+i0OHDqmXKZVKHDp0CJ06dar2fKqTlZUVBg4cCKlUCrlcDoVCgeLiYigUCsjlckilUoSFhbFTFxEREdUaejcnyM/Px8aNG3Hw4EG0bt0aderU0Vj/2WefGSy5J02ZMgUjR45Eu3bt0L59e6xatQq5ubkYPXq00V7TXMhkMoSHh6vHiU1NTYVEIkFwcDDCwsI4vBYRERHVKnoXsefOnUPbtm0BABcuXNBYZ+xxYocMGYJ79+5hzpw5uHPnDtq2bYu9e/eW6exVU8lkMvj7+yM5ORnZ2dlwdHSEr68v78ASUa2gVCp5/SMiNYsaJ7aqLHWcWCKi2o4zFhLVHkYdJ5aIiKi6cMZCIipPpYrYv//+G7t27UJycjIKCws11u3evdsgiRERESmVSkRGRiI9PV1jxkInJycEBgZCLpcjKioK/v7+bFpAVMvo/Re/c+dOdO7cWf3TTlFRES5evIjDhw/D2dnZGDkSEVEtpc+MhURUu+hdxC5atAiff/45fv31V9jY2GD16tVITEzEa6+9xnFKiYjIoDhjIRFVRO8i9tq1a3jppZcAlI7dmpubC5FIhPfffx8bN240eIJERFR7PT5jYXk4YyFR7aV3Eevq6qr+xtuwYUP1MFuZmZnIy8szbHZERFSrccZCIqqI3kVs9+7dceDAAQDA4MGDMWnSJIwdOxZvvPEGevbsafAEiYio9uKMhURUEb3Hib1//z7y8/Ph5eUFpVKJZcuW4cSJE2jevDlmzZoFV1dXY+VaZRwnlojIMpU3TqxMJuOMhUQ1kK71Gic7ICKqBM4eVf14zIlqB052QERkJJw9yjSsrKzg5+dn6jSIyEywiCUi0gNnjyIiMg/8HYaISEdPzh7l5OQEsVisnj0qPT0dUVFRUCqVpk6ViKjGYxFLRKQjzh5FRGQ+WMQSEemIs0cREZkPvdrEZmZmIjIyErGxsbhx4wby8vLg7u6OoKAg9O7dG507dzZWnkREJvf47FHl9Zjl7FFERNVHpzuxt27dwltvvQVPT08sXLgQDx8+RNu2bdGzZ094e3sjOjoaL7zwAgIDA/H9998bO2ciIpPg7FFEROZDpzuxQUFBGDlyJM6cOYPAwMByYx4+fIioqCisWrUKKSkpmDZtmkETJSIyNdXsUSkpKZDL5fD29laPTnDz5k3OHkVEVI10muwgIyMDbm5uOu9U3/jqwskOiMgQOHsUEZHxGHSyA30LUnMsYImIDEUmk8Hf35+zRxERmZDekx34+voiNDQUISEhCA0NRdOmTY2RFxGRWePsUUREpqX3bYNFixZBIpFg6dKlaN68OXx8fDB8+HB8/fXXuHLlijFyJCIiIiLSoFOb2Ircvn0bR44cwZ49e/D9999DqVSipKTEkPkZFNvEEhEREZk3g7aJfVJeXh6OHTuGmJgYREdHIy4uDi1btkRoaGhl8yUiIiIi0pneRWznzp0RFxcHmUyG0NBQTJ8+Hd27d4erq6sx8rNYSqWSnT6IiIiIjETvIjYxMREODg4ICAhAQEAAZDIZC9gnlDf8TkBAAAYOHMjhd4iIiIgMQO9bgxkZGTh8+DA6duyIffv2oUuXLmjYsCGGDh2Kr7/+2hg5WpSEhASsWbMGcXFxkEql8Pf3h1QqRVxcHNasWYOEhARTp0hERERk8arUsUsQBJw5cwZffPEFtm/fXus7dimVSixZsgRxcXEIDAyESCRSrxMEAXK5HMHBwfjoo4/YtICIiIioHEbr2PXPP/8gJiYGMTExOHbsGLKzs9GqVStMnDgRISEhVUra0iUnJyMxMRE+Pj4aBSwAiEQieHt7IyEhAcnJyRxfkoiIiKgK9C5i27dvj6CgIISEhGDs2LHo3r07nJ2djZGbxcnOzkZ+fj4cHBzKXe/g4IDU1FRkZ2dXc2ZERERENYveRez9+/c5xmoFHB0dIZFIkJubW+4xys3NhUQigaOjowmyIyIiIqo59G6Y6eTkhMzMTGzatAkzZszA/fv3AZQ2M0hNTTV4gpbE19cXAQEBSElJwZNNjQVBwM2bNyGTyeDr62uiDImIiIhqBr3vxJ47dw49e/aEi4sLkpKSMHbsWNSrVw+7d+9GcnIytm7daow8LYKVlRUGDhyIlJQUyOVyeHt7w8HBAbm5ubh58yakUinCwsLYqYuIiIioivSupqZMmYLRo0fjypUrkEgk6uX9+vXD0aNHDZqcJZLJZAgPD0dQUBAyMjJw+fJlZGRkIDg4GOHh4RwnloiIiMgA9L4Te/r0aWzYsKHM8oYNG+LOnTsGScrSyWQy+Pv7c8YuIjJLnFGQiGoCvYtYW1tbZGVllVl++fJluLu7GySpmsDKyorDaBGR2eGMgkRUU+j91XvAgAGYP38+ioqKAJSOf5qcnIyPPvoIgwYNMniCRERkGJxRkIhqEr2L2JUrVyInJwceHh54+PAhQkJC0KxZMzg6OuLTTz81Ro5ERFRFSqUSkZGRSE9PR2BgIJycnCAWi+Hk5ITAwECkp6cjKioKSqXS1KkSEelE7+YEzs7OOHDgAI4dO4Zz584hJycHwcHB6NWrlzHyIyIiA+CMgkRU0+hdxKp07doVXbt2NWQuRERkJJxRkIhqGp2LWF3Hfx0xYkSlkyEiIuPgjIJEVNPoXMROmjSpwnUikQi5ubkoLi5mEUtEZIZUMwrGxcUhMDBQo0mBakbB4OBgzihIRBZD545dDx48KPchl8vx2muvQRAEvPDCC8bMlYiIKkk1o6BUKoVcLodCoUBxcTEUCgXkcjlnFCQii1Ppq1V2djZmzZqFZ555BmfPnsW+ffuwd+9eQ+ZGREQGxBkFiagm0btjV1FREdauXYtFixbBzc0NERERePXVV42RGxERGRhnFNRkyNnLOBNa7cFzbR50LmIFQcDWrVsxZ84cFBcXY9GiRRgzZgzEYrEx8yMiIgPjjIKlDDl7GWdCqz14rs2HzkVs69at8e+//2LixImYPHky7O3tkZubWyauvF6vRERE5kQ1e1l6ejp8fHzg4OCA3NxcxMXFISUlRa/mFYbcF5k3nmvzovO974sXL+Lhw4dYtmwZGjZsCFdXV42Hi4sLXF1djZkrERFRlRly9jLOhFZ78FybH53vxEZHRxszDyIiomphyNnLOBNa7cFzbX50LmJDQkKMmQcREVG1MOTsZZwJrfbguTY/OjUnKK/tqyHjiYiIqsvjs5eVR5/Zywy5LzJvPNfmR6citlmzZliyZAlu375dYYwgCDhw4AD69u2LNWvWGCxBIiIiQ1LNXpaSkgJBEDTWqWYvk8lkOs1eZsh9kXnjuTY/OjUniImJwccff4xPPvkEbdq0Qbt27eDl5QWJRKKetevPP/+EtbU1ZsyYgXfeecfYeRMREVWKavaylJQUyOVyeHt7q3uZ37x5U6/Zywy5LzJvPNfmRyQ8+XVCi+TkZPzwww+IjY3FjRs38PDhQ0ilUgQFBaF3797o27evWY8bm5WVBWdnZygUCg4FRkRUy5U33qdMJkNYWJhBxomt7L7IvPFcG5+u9ZpeRaylYxFLZHycyYYsCWfsosrguTYuXes1vaedJSKqCGeyIUtjyNnLOBNa7cFzbR50LmKnTJny9J1ZW6NBgwbo2bMn2rRpU6XEiMiycCYbIiKqTjoXsXFxcU+NUSqVSEtLwwcffIC1a9di3LhxVUqOiCzDkzPZqAYCV81kI5fLERUVBX9/f/7kRkREBmGUGbu++eYbzJ8/n0UsUS3BmWyICGBbUapeVW4TW1xcjPz8fNStW1e9rF+/fhwrlqgW4Uw2RMQ28VTddP569Ouvv2LLli0ayz799FPUrVsXLi4uePHFF/HgwQMAgLu7O86cOWPQRInIfHEmG6LaTdUmPi4uDlKpFP7+/pBKpYiLi8OaNWuQkJBg6hSpBtK5iP3ss880/oM6ceIE5syZg9mzZ2PXrl1ISUnBggULjJIkEZk3zmRDVHs92SbeyckJYrFY3SY+PT0dUVFRUCqVpk6Vahidi9iLFy+ic+fO6uc//vgjXnjhBcycORP/+c9/sHLlSvz6669GSZKIzJtqJhupVAq5XA6FQoHi4mIoFArI5XLOZENUg+nTJp7IkHRuE5udnQ03Nzf182PHjmHw4MHq5y1atMCtW7cMmx2ZFBvokz5kMhnCw8PVbeJSU1MhkUgQHBxcqZls+PkzHzwXpA3bxJOp6FzENmzYEAkJCfD19UVOTg7i4+Px+eefq9dnZGTA3t7eKElS9WMDfaoMmUwGf3//Khc8/PyZD54LeprH28SXN7sS28STsehcxA4ePBiTJ0/Gxx9/jN9//x0NGjRAx44d1ev//vtv+Pv7GyVJql4ctJ6qoqoz2fDzZz54LkgXqjbxcXFxGuNEA4/axAcHB7NNPBmczrdH5syZg+eeew7h4eE4e/Ysvv32W4jFYvX67777Di+//LJRkqTqwwb6ZEr8/JkPngvSFdvEk6nofCdWIpFg69atFa7XZzIEMl8ctJ5MiZ8/88FzQfowdJt4Il3oXMR26dIFW7duRbNmzYyZD5kYG+iTKfHzZz54LnTDTm+PGKpNPJGudC5ivb290bZtWyxduhTjx483Zk5kQmygT6bEz5/54Ll4OnZ6K6uqbeKJ9KHz16Ndu3YhIiIC8+fPxwsvvICbN28aMy8yEQ5aT6bEz5/54LnQjjNUEZmezndigdIRCkJDQzF+/Hi0atUK//3vf2FtrbmLzz77zKAJUvVSNdBPSUmBXC6Ht7e3ukfyzZs32UCfjIqfP/PBc1GxJzu9qdoMqzq9yeVyREVFwd/fv1YeH6LqolcRCwD16tWDTCZDZGQk4uLiNIrYJxv/k2ViA30yJX7+zAfPRfnY6Y3IPOhVxF68eBEjRozA/fv3sX//fvTo0cNYeZGJsYE+mRI/f+aD56IsdnojMg86F7FLlizBJ598gqFDh2L16tW1ujF/bcEG+mRK/PyZD54LTez0RmQedP4qvXr1avzwww/YvHkz/zCJiKjWYqc3IvOg853YCxcuwM3NzZi5EBERmT12eiMyDyLhya+RNVhWVhacnZ2hUCjK/QmIiIhIV+WNEyuTyWp1pzciQ9C1XtN7dAIiIqLapryZuWpKpzfOOkaWikUsERGRFk+bmcuSO71x1jGyZCxiiYiIKqCamSs9PR0+Pj7qtq9xcXFISUlBeHi4xRZ7Nfm9Ue1QqSJWqVTi6tWrSEtLg1Kp1FjXvXt3gyRGRERkSjV5Zq6a/N6o9tC7iP3rr78wdOhQ3Lhxo8zQIiKRCCUlJQZLjoiIyFRq8sxcNfm9Ue2hdxH77rvvol27dvjtt9/g6enJqWaJiMhoTNnpqCbPzFWT3xvVHnoXsVeuXMGPP/6IZs2aGSMfIiIiAKbvdFSTZ+aqye+Nag+9v8526NABV69eNUYuREREAB51OoqLi4NUKoW/vz+kUini4uKwZs0aJCQkGD2HmjwzV01+b1R76H0nduLEiZg6dSru3LmDVq1aoU6dOhrrW7dubbDkiIio9jGXTkc1eWaumvzeqPbQe8au8j7QIpEIgiCYfccuzthFRGT+kpKSMGfOHEil0nKv1QqFAhkZGZg/f361dDqqyTNz1eT3RpbLaDN2Xb9+vUqJERERaWNunY5qysxc5anJ741qPr2L2EaNGhkjD62SkpKwYMECHD58GHfu3IGXlxeGDx+OmTNnwsbGptrzISIi4zHHTkdWVlY1dqipmvzeqGar1Fetbdu2oUuXLvDy8sKNGzcAAKtWrcLPP/9s0ORUEhMToVQqsWHDBly8eBGff/451q9fj48//tgor0dERKbDTkdEpAu9i9h169ZhypQp6NevHzIzM9VtYF1cXLBq1SpD5wcA6NOnDyIiIvDiiy+iSZMmGDBgAKZNm4bdu3cb5fWIiMh0VJ2OpFIp5HI5FAoFiouLoVAoIJfL2emIiABUoohdu3Ytvv76a8ycORNisVi9vF27djh//rxBk9NGoVCgXr16WmMKCgqQlZWl8SAiIvMnk8kQHh6OoKAgZGRk4PLly8jIyEBwcDDCw8PZ6YiIKtexKygoqMxyW1tb5ObmGiSpp7l69SrWrl2LFStWaI1bvHgx5s2bVy05ET3JlDMNEdUE7HRERNroXcQ2btwYZ8+eLdPBa+/evXp/M54+fTqWLl2qNSYhIQEBAQHq56mpqejTpw8GDx6MsWPHat12xowZmDJlivp5VlYWfHx89MqRqDJMPdMQUU3BTkdEVBG9i9gpU6Zg/PjxyM/PhyAIOHXqFL777jssXrwYmzZt0mtfU6dOxahRo7TGNGnSRP3vW7duoUePHujcuTM2btz41P3b2trC1tZWr5yIqko101B6ejp8fHzUA4jHxcUhJSWFP4USEREZgN5F7FtvvQU7OzvMmjULeXl5GDp0KLy8vLB69Wq8/vrreu3L3d0d7u7uOsWmpqaiR48eePbZZxEREcGfk8gsmctMQ0RERDWd3kUsAAwbNgzDhg1DXl4ecnJy4OHhYei8NKSmpiI0NBSNGjXCihUrcO/ePfW6Bg0aGPW1ifSRnJyMxMRE+Pj4qAtYFZFIBG9vbyQkJCA5OZk/kRIREVVBpYrY4uJixMTE4Nq1axg6dCiA0p/6nZycULduXYMmCAAHDhzA1atXcfXqVXh7e2us03PWXKokdlLSjbnNNERERFRT6V3E3rhxA3369EFycjIKCgrwwgsvwNHREUuXLkVBQQHWr19v8CRHjRr11Laz+sjNBR4bHUxNLAYkEs24ilhZAXZ2lYvNywMqqr1FIsDevnKxDx8CSmXFeTxeV+kTe/ZsIqKifsWlS5dQUFAAW1tb+Pv74+WXX0ZAQIBGbH4+8P9DB5fL3r40bwAoKACKiw0Ta2dXepwBoLAQKCoyTKxE8uizokusaqahrKyHcHBwLROTlfUQYrET7OwezTRUVFS674rY2gLW//+XWlxceiwqYmMD1Kmjf2xJSem5q0idOqXx+sYqlaWfNUPEWluXHgug9G8iL88wsfr83fMaUX7s0/7ueY0oG6vP3z2vEbrF8hpRqiZcI3Qe7ErQ0yuvvCIMHz5cKCgoEOrWrStcu3ZNEARBiI6OFpo1a6bv7qqVQqEQAAiAQig9pZqPfv004+3ty8aoHiEhmrFSacWx7dppxjZqVHFsYKBmbGBgxbGNGmnGtmtXcaxUqhkbElJxrL39ozi5XC74+p6rMPbJT9Crr1YcBwhCTs6j2JEjtcempT2KHTdOe+z1649ip03THnvhwqPYuXO1x5469Sh22TLtsdHRglBSUiJ8+umnQtu2X2uN/eWXEvV+IyK073fXrkc57NqlPTYi4lHsnj3aY7/44lFsdLT22GXLHsWeOqU9du7cR7EXLmiPnTbtUez169pjx417FJuWpj125MhHsTk52mNffVXQoC2W14jSx+PXCEEoPS7ajtvjavs1QuWLL7TH7tnzKJbXiFK8RjxS868RpfWaQqEQtNH79+DY2FjMmjULNqqvRv/Pz88Pqamp+u6OzJiqk1JhoZZbC/8fR6VUMw09rVkNm2IQERFVjUgQBEGfDVxdXXH8+HEEBgbC0dER8fHxaNKkCY4dO4ZBgwbh7t27xsq1yrKysuDs7IxbtxRwcnIqs54/A2jGJiUlYc6cOXBxaYC6dZ3LxGRlZSEjIwOLF89Sd1LiT4Wl/z53LgG7d+/RaIIREBCA/v37IyAggD8VViKWPxWWMqdrhAqbE+gfy+YEpf/mNaJysTX9GvHgQRa8vJyhUJRfr6nz17eIHTJkCJydnbFx40Y4Ojri3LlzcHd3xyuvvAJfX19ERETos7tqpSpin3ZQTMEcO06dP38eCxYsgL+/v8YUwyrFxcW4fPkyZs+ejVatWpkgQ/NmjueUiDTx75TI/Ohar+ndsWvlypXo3bs3AgMDkZ+fj6FDh+LKlSuQSqX47rvvqpR0bWWuszupOinl5uaW+yHKzc2FRCKBo6NjOVsTZxoiMm/meu0lIt3oXcR6e3sjPj4eO3fuxLlz55CTk4MxY8Zg2LBhsHv8XjfpxJxnd/L19UVAQADi4uI0Bu4HAEEQcPPmTQQHB8PX19ck+RERVZY5X3uJSDeVGifW2toaw4cPN3QutY65z+6k6qSUkpICuVwOb29v9YX+5s2bkEqlCAsL409vRGRRzP3aS0S60fuv09fXFyNGjMD//vc//Pvvv8bIqdbQZ3YnU5HJZAgPD0dQUBAyMjJw+fJlZGRkIDg4mHcqiMgiWcK1l4ieTu87sYsWLcLRo0exdOlSjB07Fg0bNkRISAhCQkIQGhqK5s2bGyPPGslSZneSyWTw9/dn5wciC8YOTI9YyrWXiLTTu4gdPny4uinB7du3ceTIEezZswfjxo2DUqlEibYxFEiDJXWcYiclIsvFDkyaLOnaS0QVq1Sb2Ly8PBw7dgwxMTGIjo5GXFwcWrZsidDQUAOnV7Ox4xQRGRs7MJXFay9RzaB3Edu5c2fExcVBJpMhNDQU06dPR/fu3eHqWnaeeNKOHaeIyJjYgal8vPYS1Qx6/4UmJibCwcEBAQEBCAgIgEwmYwFbBew4RUTGwg5MFeO1l8jy6X0nNiMjA+fPn0dMTAz27duHmTNnwsbGBiEhIejRowfGjh1rjDxrNHacIiJjYAcm7XjtJbJsek87+zhBEHDmzBl88cUX2L59u9l37DLnaWeJiAwtKSkJc+bMgVQqLfeap1AokJGRgfnz57PjJhGZDaNNO/vPP/8gJiYGMTExOHbsGLKzs9GqVStMnDgRISEhVUqaiIgMhx2YiKgm07uIbd++PYKCghASEoKxY8eie/fucHZ2NkZuRERUBezAREQ1md7NCbKysiz2p3g2JyCi2qi8cWJlMhnCwsLYgYmIzI7RmhO0bdsWp0+fhpubm8byzMxMBAcHcypaIiIzU9M7MHE2MstVHeeOn4+qMefjp3cRm5SUVG7nrYKCAqSmphokKSIiMqyaOuseZyOzXNVx7vj5qBpzP346F7G//PKL+t/79u3TaAdbUlKCQ4cO1cgLJBERmSfORma5quPc8fNRNZZw/HQuYsPCwgCUDpA9cuRIjXV16tSBn58fVq5cadDkiIiIysPZyCxXdZw7fj6qxlKOn86vrFQqoVQq4evri7S0NPVzpVKJgoICXLp0Cf379zdmrkRERAA4G5klq45zx89H1VjK8dO7fL5+/TqkUikAID8/3+AJERHVBkqlEklJSTh//jySkpKgVCpNnZJF0WU2svz8/Fo7G5k5q45zx89H1VjK8dO7Y5dSqcSnn36K9evX4+7du7h8+TKaNGmC2bNnw8/PD2PGjDFGnkRENYa5d5awBI6OjpBIJMjNzS13CJ7c3FxIJBI4OjqaIDvSpjrOHT8fVWMpx0/vO7ELFy7Eli1bsGzZMtjY2KiXt2zZEps2bTJockRENY2qs0RcXBykUin8/f0hlUoRFxeHNWvWICEhwdQpWgTVbGQpKSl4crhz1WxkMpmMs5GZoeo4d/x8VI2lHD+9i9itW7di48aNGDZsGMRisXp5mzZtkJiYaNDkiIhqkic7Szg5OUEsFqs7S6SnpyMqKopNC3Sgmo1MKpVCLpdDoVCguLgYCoUCcrmcs5GZseo4d/x8VI2lHD+9Z+yys7NDYmIiGjVqBEdHR8THx6NJkyaQy+Vo3749cnJyjJVrlXHGLiIypaSkJMyZMwdSqbTca5BCoUBGRgbmz5/PIQt1xNnILFd1nDt+PqrGVMfPaDN2BQYGIjY2Fo0aNdJY/uOPPyIoKEj/TImIaglVkSqRSKBUKuHs7KzR89fBwQGpqakm7yxhSWr6bGQ1WXWcO34+qsbcj5/eReycOXMwcuRIpKamQqlUYvfu3bh06RK2bt2KPXv2GCNHIiKLl5CQgG+//RYJCQm4cuUK7OzsIJVKERAQAHd3dwDm01nC0tTU2chqg+o4d/x8VI05Hz+9S+lXXnkFv/76Kw4ePAgHBwfMmTMHCQkJ+PXXX/HCCy8YI0ciIoum6sx1/fp1eHp6QiwWw87ODrdv38bJkydx7949s+osQURkCfS+EwsA3bp1w4EDBwydCxFRjfN4Z64WLVqgfv36OHnyJLKysuDo6AiFQoGzZ8/C09MT7u7uZtFZgojIElSqiAWAv//+Wz0UTGBgIJ599lmDJUVEVFM8OfONu7s7OnTogMTERKSnp0OpVOL27dvo0qULRo8ezc4mREQ60ruIvXnzJt544w0cP34cLi4uAIDMzEx07twZO3fuhLe3t6FzJCIye0qlstzOD+XNfOPu7g6pVAqFQoG8vDykpqZi2LBhLGCJiPSgdxH71ltvoaioCAkJCfD39wcAXLp0CaNHj8Zbb72FvXv3GjxJIiJzpm0GropmvhGJRHBxcYFIJIKbmxucnZ1N+A6IiCyP3kXskSNHcOLECXUBCwD+/v5Yu3YtunXrZtDkiIjMnarTVnp6Onx8fODg4IDc3FzExcUhJSUFEyZMQEBAAOLi4hAYGKgxpJaqM1dwcDA7cxER6Unv3gM+Pj4oKioqs7ykpAReXl4GSYqIyBLoMgPXL7/8gldeecXsZ74hIrI0el81ly9fjokTJ+Lvv/9WL/v7778xadIkrFixwqDJERGZsyc7bT1OJBLB29sbCQkJcHBwQHh4OIKCgpCRkYHLly8jIyMDwcHBCA8PZ1tYIqJK0Kk5gaurq8YFOjc3Fx06dIC1denmxcXFsLa2xptvvomwsDCjJEpEZG7K67T1uMdn4GrVqpVZz3xDRGRpdCpiV61aZeQ0iIgsT0WdtlSenIHLnGe+ISKyNDoVsSNHjjR2HkREFsfX15edtoiITIS/YxERVZKVlRUGDhzITltERCYgEgRBMHUS1SUrKwvOzs5QKBTl/vRHRJatogkHjK28cWJlMhnCwsLYaYuISE+61muVnnaWiMicaJtwwNiFpEwmY6ctIqJqxiKWiCze0yYcqI5hrNhpi4ioelX6NsHVq1exb98+PHz4EEBpJwYiouqmy4QDUVFRUCqVpk6ViIgMSO8iNiMjA7169cIzzzyDfv364fbt2wCAMWPGYOrUqQZPkIhIG10nHEhOTjZRhkREZAx6F7Hvv/8+rK2tkZycDHt7e/XyIUOGYO/evQZNjojoaXSZcCA/Px/Z2dnVnBkRERmT3m1i9+/fj3379sHb21tjefPmzXHjxg2DJUZEpAt9JxwgIqKaQe87sbm5uRp3YFXu378PW1tbgyRFRKQr1YQDKSkpZdrmqyYckMlknHCAiKiG0buI7datG7Zu3ap+LhKJoFQqsWzZMvTo0cOgyRERPQ0nHCAiqp30nuzgwoUL6NmzJ4KDg3H48GEMGDAAFy9exP3793H8+HE0bdrUWLlWGSc7IKq5OOEAEVHNoGu9VqkZuxQKBb744gvEx8cjJycHwcHBGD9+PDw9PauUtLGxiCWq2Uw1YxcR1Uy8ppiGUYtYS8UiloiIiHRhylkAazujTTsbERGBunXrYvDgwRrLf/jhB+Tl5WHkyJH6Z0tERERkJsxhFkB6Or3viS9evBhSqbTMcg8PDyxatMggSRERERGZAmcBtBx6F7HJyclo3LhxmeWNGjXijDhERERk0TgLoOXQu4j18PDAuXPnyiyPj4+Hm5ubQZIiIiIiMgXOAmg59C5i33jjDYSHhyM6OholJSUoKSnB4cOHMWnSJLz++uvGyJGIiIioWjw+C2B5OAug+dC7Y9eCBQuQlJSEnj17wtq6dHOlUokRI0awTSwRERFZNNUsgHFxcQgMDNRoUqCaBTA4OJizAJoBvYtYGxsbfP/991iwYAHi4+NhZ2eHVq1aoVGjRsbIj4iIiKjaqGYBTElJgVwuh7e3t3p0gps3b3IWQDPCcWKJiIiInsBZAE3HaOPElpSUYMuWLTh06BDS0tLKDDFx+PBh/bMlIqJqw1mIiJ5OJpPB39+ffytmTO8idtKkSdiyZQteeukltGzZsszwE0REZL44CxGR7qysrODn52fqNKgCehexO3fuxK5du9CvXz9j5ENEREbCWYiIqCbR+564jY0NmjVrZoxciIjISDgLERHVNHoXsVOnTsXq1atRi/qDERFZPM5CREQ1jd7NCY4dO4bo6Gj88ccfaNGiBerUqaOxfvfu3QZLjoiIDEOXWYhSU1M5CxERWQy9i1gXFxcMHDjQGLkQEZGRPD4LUXlD1nAWIiKyNHoXsREREcbIg4iIjIizEBFRTVOpwc6Ki4tx8OBBbNiwQf3T061bt5CTk2PQ5IiIyDBUsxBJpVLI5XIoFAoUFxdDoVBALpdzFiIisjh6z9h148YN9OnTB8nJySgoKMDly5fRpEkTTJo0CQUFBVi/fr2xcq0yzthFRLUdZyEiInNntBm7Jk2ahHbt2iE+Ph5ubm7q5QMHDsTYsWMrly0REVULzkJkPjhzGlHV6F3ExsbG4sSJE7CxsdFY7ufnh9TUVIMlRkRExsFZiEyPM6cRVZ3eRaxSqURJSUmZ5Tdv3mSvViIioqfgzGlEhqH37xYvvvgiVq1apX4uEomQk5ODuXPncipaIiIiLThzGpHh6F3ErlixAsePH0dgYCDy8/MxdOhQdVOCpUuXGiNHIiKiGoEzpxEZjt7NCXx8fBAfH4/vv/8e8fHxyMnJwZgxYzBs2DDY2dkZI0ciqgJ2HiEyH5w5jchw9Cpii4qKEBAQgD179mDYsGEYNmyYsfIiIgNg5xEi88KZ04gMR6/bMXXq1EF+fr6xciEiA1J1HomLi4NUKoW/vz+kUini4uKwZs0aJCQkmDpFolpHNXNaSkoKnhymXTVzmkwm48xpRDrQ+zfF8ePHY+nSpSguLjZGPkRkAOw8QmSeOHMakeHo3Sb29OnTOHToEPbv349WrVqVadeze/dugyVHRJWjT+cRjhdKVL1kMhnCw8PVTX1SU1MhkUgQHBzMmdOI9KB3Eevi4oJBgwYZIxciMhB2Hql+7EBH+uDMaURVp3cRGxERYYw8iMiA2HmkerEDHVUGZ04jqppKfeUrLi7GwYMHsWHDBvWdnFu3biEnJ8egyRFR5bDzSPVhBzoiItPQ+07sjRs30KdPHyQnJ6OgoAAvvPACHB0dsXTpUhQUFGD9+vXGyJOI9KDqPJKSkgK5XA5vb2/11JY3b95k5xEDebIDnar9saoDnVwuR1RUFPz9/XmsiYgMTO+r6qRJk9CuXTs8ePBAY3KDgQMH4tChQwZNjogqT9V5JCgoCBkZGbh8+TIyMjIQHBzMudkNhLMvERGZjt53YmNjY3HixAnY2NhoLFdNPUtE5oOdR4zLkjrQseMZWRJ+XkkXehexSqUSJSUlZZbfvHmzWjqJFBQUoEOHDoiPj0dcXBzatm1r9NcksmTsPGI8ltKBjh3PyJLw80q60vtrzYsvvohVq1apn4tEIuTk5GDu3Lno16+fIXMr14cffggvLy+jvw4R0dNYQgc6djwjS8LPK+lD7yJ25cqVOH78OAIDA5Gfn4+hQ4eqmxIsXbrUGDmq/fHHH9i/fz9WrFhh1NchItKFuc++xJnbyJLw80r60rs5gbe3N+Lj4/H9998jPj4eOTk5GDNmDIYNG6bR0cvQ7t69i7FjxyIqKgr29vY6bVNQUICCggL186ysLGOlR0S1lDnPvsSZ28iS8PNK+tKpiA0ODsahQ4fg6uqK+fPnY9q0aRg2bBiGDRtm7PwAlP4sN2rUKLz77rto164dkpKSdNpu8eLFmDdvnnGTI6Jaz1w70Onb8YydaciULKmjJJkHnYrYhIQE5ObmwtXVFfPmzcO7776r891QbaZPn/7UJggJCQnYv38/srOzMWPGDL32P2PGDEyZMkX9PCsrCz4+PpXKlYhIG3PsQKdPxzN2piFTs5SOkmQ+dCpi27Zti9GjR6Nr164QBAErVqxA3bp1y42dM2eOzi8+depUjBo1SmtMkyZNcPjwYfz555+wtbXVWNeuXTsMGzYM33zzTbnb2traltmGiKi2UHU8i4uL05iMAXjU8Sw4OBi5ubn44osvkJ6eDh8fH/XEGHFxcUhJSeG4wlQtdP28cqZBUhEJT3apLcelS5cwd+5cXLt2Df/88w8CAwNhbV22/hWJRPjnn38MnmRycrJGe9Zbt26hd+/e+PHHH9GhQwd4e3vrtJ+srCw4OztDoVCU+y2PiKimUfX2Tk9PL3fmtgkTJuDnn3+usHCQy+UIDg7GRx99xKYFZHRP+7zyC1XtoGu9plMR+zgrKyvcuXMHHh4eVU6yspKSktC4cWO9x4llEUtEtVF5TQVkMhnCwsJgZ2eHOXPmQCqVlntdVCgUyMjIwPz5882uuQTVTNo+ryxgawdd6zW9O3bNnTu3wqYERERkfrR1PDt//jw705BZMdeOkmR+9O7YNX/+fLz33nsG6dhVWX5+fmUGFicioopV1PGMnWnIHJljR0kyPybt2EVERKbFzjREZKl0KmK3bNmCuXPnYs+ePRCJRPjjjz8q7NjFIpaIyHKoZh1LSUmBXC4vtzONKWcdIyKqiEV27KosduwiIiofO9MQkbkwaMeux3HOYiKimkcmk6F58+b466+/cPfuXdSvXx8dO3Ys91e32o4zmxGZB52uTr/88gv69u2LOnXq4JdfftEaO2DAAIMkRkRE1ae8O7FHjx7ljF1P4MxmROZDp+YEjzch0PZtUyQSoaSkxKAJGhKbExARlfX4APOPz9iVkpLCAeYfw+NEVD10rdd0+v1DqVSq28AqlcoKH+ZcwBIRUVlKpRKRkZFIT09HYGAgnJycIBaL4eTkhMDAQKSnpyMqKqrWNyXjcSIyP2zEQ0RUiyUnJyMxMRE+Pj4aw2sBpb+ueXt7IyEhAcnJySbK0DzwOBGZH72KWKVSic2bN6N///5o2bIlWrVqhQEDBmDr1q2cfICIyAJlZ2c/dcau/Pz8Wj9jF48TkfnRuYgVBAEDBgzAW2+9hdTUVLRq1QotWrTAjRs3MGrUKAwcONCYeRIRkRE8PmNXeThjVykeJyLzo3MRu2XLFhw9ehSHDh1CXFwcvvvuO+zcuRPx8fE4ePAgDh8+jK1btxozVyIiMjDVjF0pKSllflFTzdglk8lq/YxdPE5E5kfnIva7777Dxx9/jB49epRZ9/zzz2P69OnYvn27QZMjIiLjUs3YJZVKIZfLoVAoUFxcDIVCAblczhm7/h+PE5H50XnGrgYNGmDv3r1o27Ztuevj4uLQt29f3Llzx5D5GRSH2CIiKh9n7NINjxOR8Rl8xq779++jfv36Fa6vX78+Hjx4oF+WRERkFmQyGfz9/TkT1VPwOBGZD52L2JKSEq3TD4rFYhQXFxskKSIiqn5WVlbw8/MzdRpmj8eJyDzoXMQKgoBRo0bB1ta23PUFBQUGS4qIiIiISBudi9iRI0c+NWbEiBFVSoaIiIiISBc6F7ERERHGzIOIiIiISGc6F7FEVHlKpZIdQYiIiAyIRSyRkZU3JE9AQAAGDhzIIXmIiIgqiUUskRElJCRgzZo1SE9Ph4+PDxwcHJCbm4u4uDikpKQgPDychSwREVEl8PdMIiNRKpWIjIxEeno6AgMD4eTkBLFYDCcnJwQGBiI9PR1RUVFQKpWmTpWIiMjisIglMpLk5GQkJibCx8cHIpFIY51IJIK3tzcSEhKQnJxsogyJiIgsF4tYIiPJzs5Gfn4+HBwcyl3v4OCA/Px8ZGdnV3NmRERElo9FLJGRODo6QiKRIDc3t9z1ubm5kEgkcHR0rObMiIiILB+LWCIj8fX1RUBAAFJSUiAIgsY6QRBw8+ZNyGQy+Pr6mihDIiIiy8UilshIrKysMHDgQEilUsjlcigUChQXF0OhUEAul0MqlSIsLIzjxRIREVWCSHjyFlENlpWVBWdnZygUCjg5OZk6HaolyhsnViaTISwsjMNrERERPUHXeo3jxBIZmUwmg7+/P2fsIiIiMiAWsUTVwMrKCn5+fqZOg4iIqMbgrSAiIiIisjgsYomIiIjI4rCIJSIiIiKLwyKWiIiIiCwOi1giIiIisjgsYomIiIjI4rCIJSIiIiKLwyKWiIiIiCwOi1giIiIisjgsYomIiIjI4rCIJSIiIiKLwyKWiIiIiCwOi1giIiIisjgsYomIiIjI4rCIJSIiIiKLwyKWiIiIiCwOi1giIiIisjgsYomIiIjI4rCIJSIiIiKLwyKWiIiIiCwOi1giIiIisjgsYomIiIjI4rCIJSIiIiKLwyKWiIiIiCwOi1giIiIisjgsYomIiIjI4rCIJSIiIiKLY23qBIiIiGo7pVKJ5ORkZGdnw9HREb6+vrCy4n0mIm1YxBIREZlQQkICIiMjkZiYiPz8fEgkEgQEBGDgwIGQyWSmTo/IbLGIJSIiMpGEhASsWbMG6enp8PHxgYODA3JzcxEXF4eUlBSEh4ezkCWqAH+rICIiMgGlUonIyEikp6cjMDAQTk5OEIvFcHJyQmBgINLT0xEVFQWlUmnqVInMEotYIiIiE0hOTkZiYiJ8fHwgEok01olEInh7eyMhIQHJyckmypDIvLGIJSIiMoHs7Gzk5+fDwcGh3PUODg7Iz89HdnZ2NWdGZBlYxBIREZmAo6MjJBIJcnNzy12fm5sLiUQCR0fHas6MyDKwiCUiIjIBX19fBAQEICUlBYIgaKwTBAE3b96ETCaDr6+viTIkMm+1c3SC3FxALC67XCwGJBLNuIpYWQF2dpWLzcsDnrhgqYlEgL195WIfPgS0dQB4/CcrfWLz84GSEsPE2tuX5g0ABQVAcbFhYu3sSo8zABQWAkVFhomVSB59VvSJLSoqja+IrS1gba1/bHFx6bGoiI0NUKeO/rElJaXnriJ16pTG6xurVJZ+1gwRa21deiyA0r+JvDzDxOrzd89rRPmxvEboH1tUBKvCQvynd2/c/fdfXDt3Dg0bNoS9vT3y8vKQdPs2pB4eCAsLg1VJifa/DV4jSvEaUaomXCO0HYvHCbWIQqEQAAiK0tNZ9tGvn+YG9vblxwGCEBKiGSuVVhzbrp1mbKNGFccGBmrGBgZWHNuokWZsu3YVx0qlmrEhIRXH2ttrxvbrV3Hskx+hV1/VHpuT8yh25EjtsWlpj2LHjdMee/36o9hp07THXrjwKHbuXO2xp049il22THtsdPSj2C++0B67Z8+j2IgI7bG7dj2K3bVLe2xExKPYPXu0x37xxaPY6GjtscuWPYo9dUp77Ny5j2IvXNAeO23ao9jr17XHjhv3KDYtTXvsyJGPYnNytMe++qqgQVssrxGlD14jHj2MdI34fsQIQS6Xl8byGlGK14hHavg1QgEIAASFQiFoUzvvxBIREZmxV199FVYcH5ZIK5EgCIKpk6guWVlZcHZ2huLWLTg5OZUN4M8A5cfyp0L9Y9mcoPTf/KmwcrG8RpT+m9cI/WN5jSj9N68RlYs1k2tE1oMHcPbygkKhKL9eU6VfK4vYpxwUIiIiIjINXes1jk5ARERERBaHRSwRERERWRwWsURERERkcVjEEhEREZHFYRFLRERERBaHRSwRERERWRwWsURERERkcVjEEhEREZHFYRFLRERERBaHRSwRERERWRwWsURERERkcVjEEhEREZHFYRFLRERERBaHRSwRERERWRwWsURERERkcVjEEhEREZHFYRFLRERERBaHRSwRERERWRyLKmJ/++03dOjQAXZ2dnB1dUVYWJipUyIiIiIiE7A2dQK6+umnnzB27FgsWrQIzz//PIqLi3HhwgVTp0VEREREJmARRWxxcTEmTZqE5cuXY8yYMerlgYGBJsyKiIiIiEzFIpoT/PPPP0hNTYWVlRWCgoLg6emJvn37PvVObEFBAbKysjQeRERERGT5LKKI/ffffwEAn3zyCWbNmoU9e/bA1dUVoaGhuH//foXbLV68GM7OzuqHj49PdaVMREREBqBUKpGUlITz588jKSkJSqXS1CmRmRAJgiCY6sWnT5+OpUuXao1JSEjAP//8g2HDhmHDhg14++23AZTeZfX29sbChQvxzjvvlLttQUEBCgoK1M+zsrLg4+MDhUIBJycnw70RIiIiMriEhARERkYiMTER+fn5kEgkCAgIwMCBAyGTyUydHhlJVlYWnJ2dn1qvmbRN7NSpUzFq1CitMU2aNMHt27cBaLaBtbW1RZMmTZCcnFzhtra2trC1tTVIrkRERFR9EhISsGbNGqSnp8PHxwcODg7Izc1FXFwcUlJSEB4ezkK2ljNpEevu7g53d/enxj377LOwtbXFpUuX0LVrVwBAUVERkpKS0KhRI2OnSURERNVIqVQiMjIS6enpCAwMhEgkAgA4OTkhMDAQcrkcUVFR8Pf3h5WVRbSMJCOwiNEJnJyc8O6772Lu3Lnw8fFBo0aNsHz5cgDA4MGDTZwdERERGVJycjISExPh4+OjLmBVRCIRvL29kZCQgOTkZPj5+ZkmSTI5iyhiAWD58uWwtrbGf//7Xzx8+BAdOnTA4cOH4erqaurUiIiIyICys7ORn58PBweHctc7ODggNTUV2dnZ1ZwZmROLKWLr1KmDFStWYMWKFaZOhYiIiIzI0dEREokEubm55Xbsyc3NhUQigaOjowmyI3PBhiRERERkVnx9fREQEICUlBQ8OYiSIAi4efMmZDIZfH19TZQhmQMWsURERGRWrKysMHDgQEilUsjlcigUChQXF0OhUEAul0MqlSIsLIydumo5k44TW910HXeMiIiITK+8cWJlMhnCwsI4vFYNZhHjxBIRERFVRCaTwd/fH8nJycjOzoajoyN8fX15B5YAsIglIiIiM2ZlZcVhtKhc/CpDRERERBaHRSwRERERWRwWsURERERkcVjEEhEREZHFYRFLRERERBaHRSwRERERWRwWsURERERkcVjEEhEREZHFYRFLRERERBaHRSwRERERWRwWsURERERkcVjEEhEREZHFYRFLRERERBbH2tQJVCdBEAAAWVlZJs6EiIiIiMqjqtNUdVtFalURm52dDQDw8fExcSZEREREpE12djacnZ0rXC8Snlbm1iBKpRK3bt2Co6MjRCKRqdOpFbKysuDj44OUlBQ4OTmZOh0yIp7r2oPnuvbgua49zOlcC4KA7OxseHl5wcqq4pavtepOrJWVFby9vU2dRq3k5ORk8j8Kqh4817UHz3XtwXNde5jLudZ2B1aFHbuIiIiIyOKwiCUiIiIii8MilozK1tYWc+fOha2tralTISPjua49eK5rD57r2sMSz3Wt6thFRERERDUD78QSERERkcVhEUtEREREFodFLBERERFZHBaxRERERGRxWMSSwS1evBjPPfccHB0d4eHhgbCwMFy6dMnUaVE1WLJkCUQiESZPnmzqVMhIUlNTMXz4cLi5ucHOzg6tWrXC33//beq0yMBKSkowe/ZsNG7cGHZ2dmjatCkWLFjw1LnsyfwdPXoUL7/8Mry8vCASiRAVFaWxXhAEzJkzB56enrCzs0OvXr1w5coV0yT7FCxiyeCOHDmC8ePH46+//sKBAwdQVFSEF198Ebm5uaZOjYzo9OnT2LBhA1q3bm3qVMhIHjx4gC5duqBOnTr4448/IJfLsXLlSri6upo6NTKwpUuXYt26dfjiiy+QkJCApUuXYtmyZVi7dq2pU6Mqys3NRZs2bfDll1+Wu37ZsmVYs2YN1q9fj5MnT8LBwQG9e/dGfn5+NWf6dBxii4zu3r178PDwwJEjR9C9e3dTp0NGkJOTg+DgYHz11VdYuHAh2rZti1WrVpk6LTKw6dOn4/jx44iNjTV1KmRk/fv3R/369fG///1PvWzQoEGws7PDt99+a8LMyJBEIhEiIyMRFhYGoPQurJeXF6ZOnYpp06YBABQKBerXr48tW7bg9ddfN2G2ZfFOLBmdQqEAANSrV8/EmZCxjB8/Hi+99BJ69epl6lTIiH755Re0a9cOgwcPhoeHB4KCgvD111+bOi0ygs6dO+PQoUO4fPkyACA+Ph7Hjh1D3759TZwZGdP169dx584djWu5s7MzOnTogD///NOEmZXP2tQJUM2mVCoxefJkdOnSBS1btjR1OmQEO3fuxD///IPTp0+bOhUysn///Rfr1q3DlClT8PHHH+P06dMIDw+HjY0NRo4caer0yICmT5+OrKwsBAQEQCwWo6SkBJ9++imGDRtm6tTIiO7cuQMAqF+/vsby+vXrq9eZExaxZFTjx4/HhQsXcOzYMVOnQkaQkpKCSZMm4cCBA5BIJKZOh4xMqVSiXbt2WLRoEQAgKCgIFy5cwPr161nE1jC7du3C9u3bsWPHDrRo0QJnz57F5MmT4eXlxXNNZoPNCchoJkyYgD179iA6Ohre3t6mToeM4MyZM0hLS0NwcDCsra1hbW2NI0eOYM2aNbC2tkZJSYmpUyQD8vT0RGBgoMYymUyG5ORkE2VExvLBBx9g+vTpeP3119GqVSv897//xfvvv4/FixebOjUyogYNGgAA7t69q7H87t276nXmhEUsGZwgCJgwYQIiIyNx+PBhNG7c2NQpkZH07NkT58+fx9mzZ9WPdu3aYdiwYTh79izEYrGpUyQD6tKlS5nh8i5fvoxGjRqZKCMylry8PFhZaZYIYrEYSqXSRBlRdWjcuDEaNGiAQ4cOqZdlZWXh5MmT6NSpkwkzKx+bE5DBjR8/Hjt27MDPP/8MR0dHdTsaZ2dn2NnZmTg7MiRHR8cybZ0dHBzg5ubGNtA10Pvvv4/OnTtj0aJFeO2113Dq1Cls3LgRGzduNHVqZGAvv/wyPv30U/j6+qJFixaIi4vDZ599hjfffNPUqVEV5eTk4OrVq+rn169fx9mzZ1GvXj34+vpi8uTJWLhwIZo3b47GjRtj9uzZ8PLyUo9gYE44xBYZnEgkKnd5REQERo0aVb3JULULDQ3lEFs12J49ezBjxgxcuXIFjRs3xpQpUzB27FhTp0UGlp2djdmzZyMyMhJpaWnw8vLCG2+8gTlz5sDGxsbU6VEVxMTEoEePHmWWjxw5Elu2bIEgCJg7dy42btyIzMxMdO3aFV999RWeeeYZE2SrHYtYIiIiIrI4bBNLRERERBaHRSwRERERWRwWsURERERkcVjEEhEREZHFYRFLRERERBaHRSwRERERWRwWsURERERkcVjEEhEREZHFYRFLRKSjO3fu4IUXXoCDgwNcXFyq/fW7d++OHTt2VPvrPun111/HypUrTfb6o0aNMsspMImoerGIJSKjGjVqFEQiEd59990y68aPHw+RSGQx0xF//vnnuH37Ns6ePYvLly+XWT9mzBi0atUKhYWFGst///132NjY4J9//gEAREZGomPHjnB2doajoyNatGiByZMnq+O3bNlSpkj+5ZdfcPfuXbz++uvqZX5+fhCJRPjrr780YidPnozQ0NCqvVktZs2ahU8//RQKhcJor1EVMTExEIlEEIlEsLKygrOzM4KCgvDhhx/i9u3bpk6PiAyERSwRGZ2Pjw927tyJhw8fqpfl5+djx44d8PX1NWFm+rl27RqeffZZNG/eHB4eHmXWf/7558jOzsbcuXPVyzIzMzF27FjMnj0bwcHBOHToEIYMGYJBgwbh1KlTOHPmDD799FMUFRVpfe01a9Zg9OjRsLLSvGxLJBJ89NFHhnmDOmrZsiWaNm2Kb7/9tlpfV1+XLl3CrVu3cPr0aXz00Uc4ePAgWrZsifPnz5s6NSIyABaxRGR0wcHB8PHxwe7du9XLdu/eDV9fXwQFBWnEKpVKLF68GI0bN4adnR3atGmDH3/8Ub2+pKQEY8aMUa/39/fH6tWrNfah+rl5xYoV8PT0hJubG8aPH//UQnHdunVo2rQpbGxs4O/vj23btqnX+fn54aeffsLWrVsrvHvs5OSEiIgIrFy5EidPngRQele0YcOGmDFjBgDg119/RZcuXfDBBx/A398fzzzzDMLCwvDll19WmNe9e/dw+PBhvPzyy2XWvf322/jrr7/w+++/a31vmzZtgkwmg0QiQUBAAL766iv1uldffRUTJkxQP588eTJEIhESExMBAIWFhXBwcMDBgwfVMS+//DJ27typ9TU/++wztGrVCg4ODvDx8cG4ceOQk5OjXq+647xv3z7IZDLUrVsXffr00bhbWlJSgilTpsDFxQVubm748MMPIQiC1tdV8fDwQIMGDfDMM8/g9ddfx/Hjx+Hu7o733nsPAHDhwgVYWVnh3r17AID79+/DyspK4273woUL0bVrV51ej4iqF4tYIqoWb775JiIiItTPN2/ejNGjR5eJW7x4MbZu3Yr169fj4sWLeP/99zF8+HAcOXIEQGmR6+3tjR9++AFyuRxz5szBxx9/jF27dmnsJzo6GteuXUN0dDS++eYbbNmyBVu2bKkwv8jISEyaNAlTp07FhQsX8M4772D06NGIjo4GAJw+fRp9+vTBa6+9htu3b5cpnFV69OiBcePGYeTIkfjhhx+wa9cubN26FdbW1gCABg0a4OLFi7hw4YLOx+7YsWOwt7eHTCYrs65x48Z49913MWPGDCiVynK33759O+bMmYNPP/0UCQkJWLRoEWbPno1vvvkGABASEoKYmBh1/JEjRyCVStXLTp8+jaKiInTu3Fkd0759e5w6dQoFBQUV5m1lZYU1a9bg4sWL+Oabb3D48GF8+OGHGjF5eXlYsWIFtm3bhqNHjyI5ORnTpk1Tr1+5ciW2bNmCzZs349ixY7h//z4iIyOfeszKY2dnh3fffRfHjx9HWloaWrRoATc3N/VnKzY2VuO56lgYs2kGEVWBQERkRCNHjhReeeUVIS0tTbC1tRWSkpKEpKQkQSKRCPfu3RNeeeUVYeTIkYIgCEJ+fr5gb28vnDhxQmMfY8aMEd54440KX2P8+PHCoEGDNF6zUaNGQnFxsXrZ4MGDhSFDhlS4j86dOwtjx47VWDZ48GChX79+6ueP56pNXl6e4O/vL1hZWQmff/65xrqcnByhX79+AgChUaNGwpAhQ4T//e9/Qn5+vjomIiJCcHZ2Vj///PPPhSZNmpR5nUaNGgmff/65kJaWJjg6Ogpbt24VBEEQJk2aJISEhKjjmjZtKuzYsUNj2wULFgidOnUSBEEQzp07J4hEIiEtLU24f/++YGNjIyxYsEB9vBYuXCh07txZY/v4+HgBgJCUlPTU46Hyww8/CG5ubhrvE4Bw9epV9bIvv/xSqF+/vvq5p6ensGzZMvXzoqIiwdvbW3jllVcqfJ3o6GgBgPDgwYMy6/744w8BgHDy5ElBEAThP//5jzB+/HhBEARh8uTJwgcffCC4uroKCQkJQmFhoWBvby/s379f5/dIRNWHd2KJqFq4u7vjpZdewpYtWxAREYGXXnoJUqlUI+bq1avIy8vDCy+8gLp166ofW7duxbVr19RxX375JZ599lm4u7ujbt262LhxI5KTkzX21aJFC4jFYvVzT09PpKWlVZhfQkICunTporGsS5cuSEhI0Pu92tnZYdq0abC3t8ekSZM01jk4OOC3337D1atXMWvWLNStWxdTp05F+/btkZeXV+7+Hj58CIlEUuHrubu7Y9q0aZgzZ06ZTmW5ubm4du0axowZo3FMFy5cqD6mLVu2RL169XDkyBHExsYiKCgI/fv3V9+RLO9upJ2dHQBUmDMAHDx4ED179kTDhg3h6OiI//73v8jIyNDYxt7eHk2bNlU/f/w8KRQK3L59Gx06dFCvt7a2Rrt27Sp8zacR/r8pgkgkAqB5F/rIkSN4/vnn0b17d8TExKjvQD/5uSAi82Bt6gSIqPZ488031W0vy2sDqmov+dtvv6Fhw4Ya62xtbQEAO3fuxLRp07By5Up06tQJjo6OWL58uboNqkqdOnU0notEogp/bjcGa2triMVidbH0pKZNm6Jp06Z46623MHPmTDzzzDP4/vvvy21iIZVK8eDBA62vN2XKFHz11VcabV2BR8f066+/1igGAaiLfJFIpC7cbG1tERoaitatW6OgoAAXLlzAiRMnNH7iB0rbjwKlBXR5kpKS0L9/f7z33nv49NNPUa9ePRw7dgxjxoxBYWEh7O3tAZR/ngQd27xWhupLiZ+fHwAgNDQUkydPxpUrVyCXy9G1a1ckJiYiJiYGDx48QLt27dS5EpF54Z1YIqo2ffr0QWFhIYqKitC7d+8y6wMDA2Fra4vk5GQ0a9ZM4+Hj4wMAOH78ODp37oxx48YhKCgIzZo107hLW1kymQzHjx/XWHb8+HEEBgZWed9P4+fnB3t7e+Tm5pa7PigoCHfu3NFayNatWxezZ8/Gp59+iuzsbPXy+vXrw8vLC//++2+ZY9q4cWN1nOqOZExMDEJDQ2FlZYXu3btj+fLlKCgoKHM38sKFC/D29i5zN13lzJkzUCqVWLlyJTp27IhnnnkGt27d0uewwNnZGZ6enhpfUIqLi3HmzBm99qPy8OFDbNy4Ed27d1cX361atYKrqysWLlyItm3bom7duggNDcWRI0fUx4KIzBPvxBJRtRGLxeo7YY//1K/i6OiIadOm4f3334dSqUTXrl2hUChw/PhxODk5YeTIkWjevDm2bt2Kffv2oXHjxti2bRtOnz6tUZBVxgcffIDXXnsNQUFB6NWrF3799Vfs3r1bo0e+IXzyySfIy8tDv3790KhRI2RmZmLNmjUoKirCCy+8UO42QUFBkEqlOH78OPr371/hvt9++218/vnn2LFjh8Zd13nz5iE8PBzOzs7o06cPCgoK8Pfff+PBgweYMmUKgNI7ku+//z5sbGzUvfFDQ0Mxbdo0PPfcc3BwcNB4rdjYWLz44osV5tKsWTMUFRVh7dq1ePnll3H8+HGsX79e5+OkMmnSJCxZsgTNmzdHQEAAPvvsM2RmZuq0bVpaGvLz85GdnY0zZ85g2bJlSE9P1xglQ3UXevv27eq7zaq70IcOHVIfHyIyP7wTS0TVysnJCU5OThWuX7BgAWbPno3FixdDJpOhT58++O2339RF6jvvvIP//Oc/GDJkCDp06ICMjAyMGzeuynmFhYVh9erVWLFiBVq0aIENGzYgIiLC4HfiQkJC8O+//2LEiBEICAhA3759cefOHezfvx/+/v4ASkdgUI1mAJQW/KNHj8b27du17rtOnTpY8H/t3DGKwkAUxvEvhX2wEpuUKTRYeIA0cwEPIGn0BN5AC2Mjgo2FEotYa51DpPIQnkDSiFvIygZZcIslGfz/ygyBN1N9PB5vNlNRFKXvo9FI2+1WSZIoCAKFYaj9fl8K/kEQyHXdZzdSeoTY2+328gZFUeh0Omk8Hv9aS6/X03K51GKxULfb1eFw0Hw+f+uNfppMJhoOh4qi6Dk+MhgM3vrX93212231+33FcSxjjM7n80t3PQzD0j2/u9CO4zAPC9SYc//P4SMAwJ/Fcaw0TUtruC6XizqdjvI8l+d5FVb32Kd7PB6VZVmldQD4bHRiAaAmrter8jxXkiQyxpTOWq2WdrvdyxaGKjQaDa3X66rLAPDh6MQCQE2sVitNp1MZY7TZbNRsNqsuCQBqixALAAAA6zBOAAAAAOsQYgEAAGAdQiwAAACsQ4gFAACAdQixAAAAsA4hFgAAANYhxAIAAMA6hFgAAABY5wvYpcX7kkFQYgAAAABJRU5ErkJggg==", "text/plain": ["<Figure size 800x600 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["Regression Slope: -0.18697989084192823\n", "Regression Intercept: -1.079540124929961\n", "p-value for the slope: 0.09208338755613493\n", "The slope is not significantly different from 0.\n"]}], "source": ["import pandas as pd\n", "import numpy as np\n", "import matplotlib.pyplot as plt\n", "from scipy import stats\n", "import os\n", "\n", "\n", "os.chdir(\"D:\\E3A\\亿杉算法实习\\温度补偿\\医院临床测试记录\")\n", "# 1. 加载数据\n", "file_path = \"4.25记录 _除去异常.xlsx\"  # 这里替换为你的文件路径\n", "data = pd.read_excel(file_path)\n", "\n", "\n", "# 2. 提取“YSJ(Old)”和“YSJ(New)”列\n", "x = data['YSJ20(New)']  # 自变量\n", "y = data['戴维（胸前）']  # 因变量\n", "\n", "# 3. 计算差异和平均值\n", "diff = x - y  # 差异\n", "mean = (x + y) / 2  # 平均值\n", "\n", "# 4. 计算95%的限度（Bland-Altman Limits）\n", "mean_diff = np.mean(diff)\n", "std_diff = np.std(diff)\n", "upper_limit = mean_diff + 1.96 * std_diff\n", "lower_limit = mean_diff - 1.96 * std_diff\n", "\n", "# 5. 绘制Bland-Alt<PERSON>图\n", "plt.figure(figsize=(8,6))\n", "plt.scatter(mean, diff, color='black', alpha=0.5)\n", "plt.axhline(mean_diff, color='blue', linestyle='--', label=f'Mean Difference: {mean_diff:.2f}')\n", "plt.axhline(upper_limit, color='red', linestyle='--', label=f'Upper Limit: {upper_limit:.2f}')\n", "plt.axhline(lower_limit, color='red', linestyle='--', label=f'Lower Limit: {lower_limit:.2f}')\n", "plt.xlabel('Mean of YSJ(New) and Dw')\n", "plt.ylabel('Difference between YSJ(New) and Dw')\n", "plt.title('Bland-Altman Plot: <PERSON><PERSON><PERSON>(New) and Dw')\n", "plt.legend()\n", "plt.show()\n", "\n", "# 6. 进行回归分析（差异与平均值之间的关系）\n", "slope, intercept, r_value, p_value, std_err = stats.linregress(mean, diff)\n", "print(f\"Regression Slope: {slope}\")\n", "print(f\"Regression Intercept: {intercept}\")\n", "print(f\"p-value for the slope: {p_value}\")\n", "\n", "# 检查斜率和截距是否显著\n", "if p_value < 0.001:\n", "    print(\"The slope is significantly different from 0 (p < 0.001).\")\n", "else:\n", "    print(\"The slope is not significantly different from 0.\")\n"]}, {"cell_type": "code", "execution_count": 20, "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 800x600 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["Regression Slope: 0.06529994191926362\n", "Regression Intercept: -1.5061093244348969\n", "p-value for the slope: 0.48407274521145804\n", "The slope is not significantly different from 0.\n"]}], "source": ["import pandas as pd\n", "import numpy as np\n", "import matplotlib.pyplot as plt\n", "from scipy import stats\n", "import os\n", "\n", "\n", "os.chdir(\"D:\\E3A\\亿杉算法实习\\温度补偿\\医院临床测试记录\")\n", "# 1. 加载数据\n", "file_path = \"4.25记录 _除去异常.xlsx\"  # 这里替换为你的文件路径\n", "data = pd.read_excel(file_path)\n", "\n", "\n", "# 2. 提取“YSJ(Old)”和“YSJ(New)”列\n", "x = data['YSJ20(Old)']  # 自变量\n", "y = data['戴维（胸前）']  # 因变量\n", "\n", "# 3. 计算差异和平均值\n", "diff = x - y  # 差异\n", "mean = (x + y) / 2  # 平均值\n", "\n", "# 4. 计算95%的限度（Bland-Altman Limits）\n", "mean_diff = np.mean(diff)\n", "std_diff = np.std(diff)\n", "upper_limit = mean_diff + 1.96 * std_diff\n", "lower_limit = mean_diff - 1.96 * std_diff\n", "\n", "# 5. 绘制Bland-Alt<PERSON>图\n", "plt.figure(figsize=(8,6))\n", "plt.scatter(mean, diff, color='black', alpha=0.5)\n", "plt.axhline(mean_diff, color='blue', linestyle='--', label=f'Mean Difference: {mean_diff:.2f}')\n", "plt.axhline(upper_limit, color='red', linestyle='--', label=f'Upper Limit: {upper_limit:.2f}')\n", "plt.axhline(lower_limit, color='red', linestyle='--', label=f'Lower Limit: {lower_limit:.2f}')\n", "plt.xlabel('Mean of YSJ(Old) and Dw')\n", "plt.ylabel('Difference between YS<PERSON>(Old) and Dw')\n", "plt.title('Bland-Altman Plot: YS<PERSON>(Old) and Dw')\n", "plt.legend()\n", "plt.show()\n", "\n", "# 6. 进行回归分析（差异与平均值之间的关系）\n", "slope, intercept, r_value, p_value, std_err = stats.linregress(mean, diff)\n", "print(f\"Regression Slope: {slope}\")\n", "print(f\"Regression Intercept: {intercept}\")\n", "print(f\"p-value for the slope: {p_value}\")\n", "\n", "# 检查斜率和截距是否显著\n", "if p_value < 0.001:\n", "    print(\"The slope is significantly different from 0 (p < 0.001).\")\n", "else:\n", "    print(\"The slope is not significantly different from 0.\")\n"]}, {"cell_type": "code", "execution_count": 33, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python310\\site-packages\\openpyxl\\worksheet\\_reader.py:312: UserWarning: Sparkline Group extension is not supported and will be removed\n", "  warn(msg)\n"]}, {"data": {"image/png": "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", "text/plain": ["<Figure size 800x600 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["Regression Slope: nan\n", "Regression Intercept: nan\n", "p-value for the slope: nan\n", "The slope is not significantly different from 0.\n"]}], "source": ["import pandas as pd\n", "import numpy as np\n", "import matplotlib.pyplot as plt\n", "from scipy import stats\n", "import os\n", "\n", "\n", "os.chdir(\"D:\\E3A\\亿杉算法实习\\温度补偿\\\\60台新仪器测试记录\")\n", "# 1. 加载数据\n", "file_path = \"4.14同一仪器一致性.xlsx\"  # 这里替换为你的文件路径\n", "data = pd.read_excel(file_path,sheet_name='Uncalibrated')\n", "# print(data.columns)\n", "\n", "\n", "# 2. 提取“YSJ(Old)”和“YSJ(New)”列\n", "x = data['E_R']  # 自变量\n", "y = data[\"7_R\"]  # 因变量\n", "\n", "# 3. 计算差异和平均值\n", "diff = x - y  # 差异\n", "mean = (x + y) / 2  # 平均值\n", "\n", "# 4. 计算95%的限度（Bland-Altman Limits）\n", "mean_diff = np.mean(diff)\n", "std_diff = np.std(diff)\n", "upper_limit = mean_diff + 1.96 * std_diff\n", "lower_limit = mean_diff - 1.96 * std_diff\n", "\n", "# 5. 绘制Bland-Alt<PERSON>图\n", "plt.figure(figsize=(8,6))\n", "plt.scatter(mean, diff, color='black', alpha=0.5)\n", "plt.axhline(mean_diff, color='blue', linestyle='--', label=f'Mean Difference: {mean_diff:.2f}')\n", "plt.axhline(upper_limit, color='red', linestyle='--', label=f'Upper Limit: {upper_limit:.2f}')\n", "plt.axhline(lower_limit, color='red', linestyle='--', label=f'Lower Limit: {lower_limit:.2f}')\n", "plt.xlabel('Mean of YSJ(Old) and YS<PERSON>(New)')\n", "plt.ylabel('Difference between <PERSON>S<PERSON>(Old) and Y<PERSON><PERSON>(New)')\n", "plt.title('Bland-Altman Plot: <PERSON><PERSON><PERSON>(Old) vs <PERSON><PERSON><PERSON>(New)')\n", "plt.legend()\n", "plt.show()\n", "\n", "# 6. 进行回归分析（差异与平均值之间的关系）\n", "slope, intercept, r_value, p_value, std_err = stats.linregress(mean, diff)\n", "print(f\"Regression Slope: {slope}\")\n", "print(f\"Regression Intercept: {intercept}\")\n", "print(f\"p-value for the slope: {p_value}\")\n", "\n", "# 检查斜率和截距是否显著\n", "if p_value < 0.001:\n", "    print(\"The slope is significantly different from 0 (p < 0.001).\")\n", "else:\n", "    print(\"The slope is not significantly different from 0.\")\n"]}], "metadata": {"colab": {"provenance": []}, "kernelspec": {"display_name": "Python 3", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.9"}}, "nbformat": 4, "nbformat_minor": 0}