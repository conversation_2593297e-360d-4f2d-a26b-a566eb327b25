{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "markdown", "metadata": {}, "source": ["应用（B-G）与R的线性拟合关系"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "============================================================ 仪器1优化预测结果 ============================================================\n"]}, {"ename": "KeyError", "evalue": "\"['仪器1_G(0-2)', '仪器1_B(0-2)'] not in index\"", "output_type": "error", "traceback": ["\u001b[1;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[1;31m<PERSON><PERSON><PERSON><PERSON><PERSON>\u001b[0m                                  <PERSON><PERSON> (most recent call last)", "Cell \u001b[1;32mIn[3], line 64\u001b[0m\n\u001b[0;32m     61\u001b[0m \u001b[38;5;28;01mfor\u001b[39;00m r \u001b[38;5;129;01min\u001b[39;00m ranges:\n\u001b[0;32m     62\u001b[0m     \u001b[38;5;66;03m# 提取当前区间数据\u001b[39;00m\n\u001b[0;32m     63\u001b[0m     cols \u001b[38;5;241m=\u001b[39m [\u001b[38;5;124mf\u001b[39m\u001b[38;5;124m'\u001b[39m\u001b[38;5;124m仪器\u001b[39m\u001b[38;5;132;01m{\u001b[39;00minstrument_new\u001b[38;5;132;01m}\u001b[39;00m\u001b[38;5;124m_G(\u001b[39m\u001b[38;5;132;01m{\u001b[39;00mr\u001b[38;5;132;01m}\u001b[39;00m\u001b[38;5;124m)\u001b[39m\u001b[38;5;124m'\u001b[39m, \u001b[38;5;124mf\u001b[39m\u001b[38;5;124m'\u001b[39m\u001b[38;5;124m仪器\u001b[39m\u001b[38;5;132;01m{\u001b[39;00minstrument_new\u001b[38;5;132;01m}\u001b[39;00m\u001b[38;5;124m_B(\u001b[39m\u001b[38;5;132;01m{\u001b[39;00mr\u001b[38;5;132;01m}\u001b[39;00m\u001b[38;5;124m)\u001b[39m\u001b[38;5;124m'\u001b[39m, \u001b[38;5;124mf\u001b[39m\u001b[38;5;124m'\u001b[39m\u001b[38;5;124m仪器A_R(\u001b[39m\u001b[38;5;132;01m{\u001b[39;00mr\u001b[38;5;132;01m}\u001b[39;00m\u001b[38;5;124m)\u001b[39m\u001b[38;5;124m'\u001b[39m]\n\u001b[1;32m---> 64\u001b[0m     instrument_new_data \u001b[38;5;241m=\u001b[39m \u001b[43mdata\u001b[49m\u001b[43m[\u001b[49m\u001b[43mcols\u001b[49m\u001b[43m]\u001b[49m\u001b[38;5;241m.\u001b[39mdropna()\u001b[38;5;241m.\u001b[39mcopy()\n\u001b[0;32m     65\u001b[0m     instrument_new_data\u001b[38;5;241m.\u001b[39mcolumns \u001b[38;5;241m=\u001b[39m [\u001b[38;5;124m'\u001b[39m\u001b[38;5;124mG\u001b[39m\u001b[38;5;124m'\u001b[39m, \u001b[38;5;124m'\u001b[39m\u001b[38;5;124mB\u001b[39m\u001b[38;5;124m'\u001b[39m, \u001b[38;5;124m'\u001b[39m\u001b[38;5;124m真实R\u001b[39m\u001b[38;5;124m'\u001b[39m]\n\u001b[0;32m     67\u001b[0m     \u001b[38;5;66;03m# 进行预测\u001b[39;00m\n", "File \u001b[1;32m~\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python310\\site-packages\\pandas\\core\\frame.py:3811\u001b[0m, in \u001b[0;36mDataFrame.__getitem__\u001b[1;34m(self, key)\u001b[0m\n\u001b[0;32m   3809\u001b[0m     \u001b[38;5;28;01mif\u001b[39;00m is_iterator(key):\n\u001b[0;32m   3810\u001b[0m         key \u001b[38;5;241m=\u001b[39m \u001b[38;5;28mlist\u001b[39m(key)\n\u001b[1;32m-> 3811\u001b[0m     indexer \u001b[38;5;241m=\u001b[39m \u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mcolumns\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43m_get_indexer_strict\u001b[49m\u001b[43m(\u001b[49m\u001b[43mkey\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[38;5;124;43mcolumns\u001b[39;49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[43m)\u001b[49m[\u001b[38;5;241m1\u001b[39m]\n\u001b[0;32m   3813\u001b[0m \u001b[38;5;66;03m# take() does not accept boolean indexers\u001b[39;00m\n\u001b[0;32m   3814\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;28mgetattr\u001b[39m(indexer, \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mdtype\u001b[39m\u001b[38;5;124m\"\u001b[39m, \u001b[38;5;28;01mNone\u001b[39;00m) \u001b[38;5;241m==\u001b[39m \u001b[38;5;28mbool\u001b[39m:\n", "File \u001b[1;32m~\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python310\\site-packages\\pandas\\core\\indexes\\base.py:6108\u001b[0m, in \u001b[0;36mIndex._get_indexer_strict\u001b[1;34m(self, key, axis_name)\u001b[0m\n\u001b[0;32m   6105\u001b[0m \u001b[38;5;28;01melse\u001b[39;00m:\n\u001b[0;32m   6106\u001b[0m     keyarr, indexer, new_indexer \u001b[38;5;241m=\u001b[39m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_reindex_non_unique(keyarr)\n\u001b[1;32m-> 6108\u001b[0m \u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43m_raise_if_missing\u001b[49m\u001b[43m(\u001b[49m\u001b[43mkeyarr\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mindexer\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43maxis_name\u001b[49m\u001b[43m)\u001b[49m\n\u001b[0;32m   6110\u001b[0m keyarr \u001b[38;5;241m=\u001b[39m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mtake(indexer)\n\u001b[0;32m   6111\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;28misinstance\u001b[39m(key, Index):\n\u001b[0;32m   6112\u001b[0m     \u001b[38;5;66;03m# GH 42790 - Preserve name from an Index\u001b[39;00m\n", "File \u001b[1;32m~\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python310\\site-packages\\pandas\\core\\indexes\\base.py:6171\u001b[0m, in \u001b[0;36mIndex._raise_if_missing\u001b[1;34m(self, key, indexer, axis_name)\u001b[0m\n\u001b[0;32m   6168\u001b[0m     \u001b[38;5;28;01mraise\u001b[39;00m \u001b[38;5;167;01mKeyError\u001b[39;00m(\u001b[38;5;124mf\u001b[39m\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mNone of [\u001b[39m\u001b[38;5;132;01m{\u001b[39;00mkey\u001b[38;5;132;01m}\u001b[39;00m\u001b[38;5;124m] are in the [\u001b[39m\u001b[38;5;132;01m{\u001b[39;00maxis_name\u001b[38;5;132;01m}\u001b[39;00m\u001b[38;5;124m]\u001b[39m\u001b[38;5;124m\"\u001b[39m)\n\u001b[0;32m   6170\u001b[0m not_found \u001b[38;5;241m=\u001b[39m \u001b[38;5;28mlist\u001b[39m(ensure_index(key)[missing_mask\u001b[38;5;241m.\u001b[39mnonzero()[\u001b[38;5;241m0\u001b[39m]]\u001b[38;5;241m.\u001b[39munique())\n\u001b[1;32m-> 6171\u001b[0m \u001b[38;5;28;01mraise\u001b[39;00m \u001b[38;5;167;01mKeyError\u001b[39;00m(\u001b[38;5;124mf\u001b[39m\u001b[38;5;124m\"\u001b[39m\u001b[38;5;132;01m{\u001b[39;00mnot_found\u001b[38;5;132;01m}\u001b[39;00m\u001b[38;5;124m not in index\u001b[39m\u001b[38;5;124m\"\u001b[39m)\n", "\u001b[1;31m<PERSON><PERSON><PERSON><PERSON>r\u001b[0m: \"['仪器1_G(0-2)', '仪器1_B(0-2)'] not in index\""]}], "source": ["import math\n", "import pandas as pd\n", "import os\n", "import numpy as np\n", "\n", "os.chdir(\"D:\\E3A\\亿杉算法实习\\温度补偿\\新探头_亿光led\")\n", "\n", "# 设置显示选项\n", "pd.set_option('display.max_rows', None)\n", "pd.set_option('display.max_columns', None)\n", "pd.set_option('display.float_format', '{:.3f}'.format)\n", "\n", "# 从机器学习结果获取优化参数\n", "ML_COEF = {\n", "    'L1':    -0.777727,\n", "    'L2':    0.336785,\n", "    'L3':    -0.323763,\n", "    'L4':    0.158168,\n", "    'scale': 38.185883,\n", "    'bias':  7.030023\n", "\n", "}\n", "\n", "def calculate_Y_optimized(<PERSON>, Dark):\n", "    \"\"\"使用机器学习优化后的预测函数\"\"\"\n", "    # 固定参数\n", "    g0 = 2000\n", "    b0 = 3000\n", "    g1 = 27\n", "    b1 = 53\n", "    d0 = 8\n", "    \n", "    try:\n", "        # 计算中间变量\n", "        L1 = math.log10(Blue - Dark - b1) / math.log10(b0 - d0 - b1) + math.log10((Blue - Dark) / b0)\n", "        L2 = math.log10(Blue - Dark - g1) / math.log10(g0 - d0 - g1) + math.log10((Blue - Dark) / g0)\n", "        L3 = math.log10((Blue - Dark)/(g0 - g1)) - math.log10((Blue - Dark)/(b0 - b1))\n", "        L4 = math.log10((Blue - Dark)/(g0 - g1)) / math.log10((Blue - Dark)/(b0 - b1))\n", "\n", "        # 应用优化系数\n", "        X = (ML_COEF['L1'] * L1 + \n", "             ML_COEF['L2'] * L2 + \n", "             ML_COEF['L3'] * L3 + \n", "             ML_COEF['L4'] * L4)\n", "        \n", "        return ML_COEF['scale'] * X + ML_COEF['bias']\n", "    except:\n", "        return np.nan\n", "\n", "# 加载数据\n", "file_path = \"2.24新探头大范围.xlsx\"\n", "data = pd.read_excel(file_path)\n", "instrument_new = \"1\"\n", "\n", "# 定义区间列表\n", "ranges = ['0-2', '2-4', '4-6', '6-8']\n", "\n", "# -------------------------- 新仪器4预测 --------------------------\n", "print(\"\\n\" + \"=\"*60 + f\" 仪器{instrument_new}优化预测结果 \" + \"=\"*60)\n", "\n", "for r in ranges:\n", "    # 提取当前区间数据\n", "    cols = [f'仪器{instrument_new}_G({r})', f'仪器{instrument_new}_B({r})', f'仪器A_R({r})']\n", "    instrument_new_data = data[cols].dropna().copy()\n", "    instrument_new_data.columns = ['G', 'B', '真实R']\n", "    \n", "    # 进行预测\n", "    instrument_new_data['预测R'] = instrument_new_data.apply(\n", "        lambda row: calculate_Y_optimized(\n", "            Blue=row['B'],\n", "            Dark=2\n", "        ), \n", "        axis=1\n", "    )\n", "    \n", "    # 输出结果\n", "    print(f\"\\n{r}区间预测结果：\")\n", "    print(instrument_new_data[['G', 'B', '预测R', '真实R']].round(3).to_string(index=False))\n", "    print(f\"预测方差: {instrument_new_data['预测R'].var():.4f}\")\n", "    print(f\"均方误差(MSE): {np.mean((instrument_new_data['预测R']-instrument_new_data['真实R'])**2):.4f}\")\n", "\n", "# -------------------------- 仪器A基准测试 --------------------------\n", "print(\"\\n\" + \"=\"*60 + \" 仪器A基准测试 \" + \"=\"*60)\n", "\n", "for r in ranges:\n", "    # 提取当前区间数据\n", "    cols = [f'仪器A_G({r})', f'仪器A_B({r})', f'仪器A_R({r})']\n", "    instrumentA_data = data[cols].dropna().copy()\n", "    instrumentA_data.columns = ['G', 'B', '真实R']\n", "    \n", "    # 进行预测（使用原始参数）\n", "    instrumentA_data['预测R'] = instrumentA_data.apply(\n", "        lambda row: calculate_Y_optimized(\n", "            Blue=row['B'],\n", "            Dark=2\n", "        ), \n", "        axis=1\n", "    )\n", "    \n", "    # 输出结果\n", "    print(f\"\\n{r}区间基准测试：\")\n", "    print(instrumentA_data[['G', 'B', '预测R', '真实R']].round(3).to_string(index=False))\n", "    print(f\"基准方差: {instrumentA_data['真实R'].var():.4f}\")\n", "    print(f\"基准MSE: {np.mean((instrumentA_data['预测R']-instrumentA_data['真实R'])**2):.4f}\")"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "Prediction results comparison:\n", " 区间    G   B   预测R  真实R\n", "0-2 1298 890 1.223 1.27\n", "0-2 1271 861 1.815 1.72\n", "0-2 1246 829 2.522 2.10\n", "2-4 1238 789 3.588 3.14\n", "2-4 1211 765 4.139 3.31\n", "2-4 1230 782 3.746 2.85\n", "4-6 1117 645 7.546 6.66\n", "4-6 1135 650 7.492 6.79\n", "4-6 1118 644 7.591 6.66\n", "6-8  949 592 8.199 7.75\n", "6-8  945 562 9.440 7.87\n", "6-8 1011 588 8.943 8.19\n", "Overall prediction variance: 8.0429\n", "\n", "Variance per range:\n", "0-2 range variance: 0.4230\n", "2-4 range variance: 0.0805\n", "4-6 range variance: 0.0025\n", "6-8 range variance: 0.3901\n"]}], "source": ["#仪器1机器学习调试\n", "import math\n", "import pandas as pd\n", "import numpy as np\n", "from scipy.optimize import minimize\n", "\n", "# Data preprocessing function\n", "def prepare_data(data):\n", "    \"\"\"将分区间列重组为长格式\"\"\"\n", "    ranges = ['0-2', '2-4', '4-6', '6-8']\n", "    merged = []\n", "    \n", "    for r in ranges:\n", "        # Extract data for each range, ensuring no NaN values in '位置'\n", "        subset = data[data['位置'].str.contains(r, na=False)].copy()\n", "        subset['range'] = r\n", "        merged.append(subset)\n", "    \n", "    return pd.concat(merged).reset_index(drop=True)\n", "\n", "# Function to calculate the optimized Y value\n", "def calculate_Y_optimized(Blue, Dark, Green, ML_COEF):\n", "    \"\"\"使用机器学习优化后的预测函数\"\"\"\n", "    # Fixed parameters\n", "    g0 = 3000\n", "    b0 = 3048\n", "    g1 = 150\n", "    b1 = 150\n", "    d0 = 8\n", "    \n", "    try:\n", "        # Calculate intermediate variables\n", "        L1 = math.log10(Blue - Dark - b1) / math.log10(b0 - d0 - b1) + math.log10((Blue - Dark) / b0)\n", "        L2 = math.log10(Green - Dark - g1) / math.log10(g0 - d0 - g1) + math.log10((Green - Dark) / g0)\n", "        L3 = math.log10((Green - Dark)/(g0 - g1)) - math.log10((Blue - Dark)/(b0 - b1))\n", "        L4 = math.log10((Green - Dark)/(g0 - g1)) / math.log10((Blue - Dark)/(b0 - b1))\n", "        \n", "        # Apply optimized coefficients\n", "        X = (ML_COEF['L1'] * L1 + \n", "             ML_COEF['L2'] * L2 + \n", "             ML_COEF['L3'] * L3 + \n", "             ML_COEF['L4'] * L4)\n", "        \n", "        return ML_COEF['scale'] * X + ML_COEF['bias']\n", "    except:\n", "        return np.nan\n", "\n", "# Load data\n", "data = pd.read_excel(\"3.4新探头对比1,3号.xlsx\")\n", "\n", "# Prepare data\n", "prepared_data = prepare_data(data)\n", "\n", "# Function to run optimization for a specific instrument\n", "def optimize_instrument(instrument_number):\n", "    # Select data for the specified instrument\n", "    if instrument_number == 1:\n", "        instrument_data = prepared_data[['range', '仪器1_G', '仪器1_B', '仪器A_R']].rename(columns={'仪器1_G': 'G', '仪器1_B': 'B', '仪器A_R': 'A_R'})\n", "    elif instrument_number == 3:\n", "        instrument_data = prepared_data[['range', '仪器3_G', '仪器3_B', '仪器A_R']].rename(columns={'仪器3_G': 'G', '仪器3_B': 'B', '仪器A_R': 'A_R'})\n", "    else:\n", "        raise ValueError(\"Invalid instrument number. Choose 1 or 3.\")\n", "    \n", "\n", "    # Extract optimized parameters\n", "    ML_COEF = {\n", "        'L1':    -1.489406,\n", "        'L2':    0.996337,\n", "        'L3':   -0.267712,\n", "        'L4':    0.369078,\n", "        'scale': 39.102222,\n", "        'bias': -9.835489,\n", "    }\n", "\n", "    \n", "    # Validate the final results\n", "    predictions = []\n", "    for _, row in instrument_data.iterrows():\n", "        pred = calculate_Y_optimized(row['B'], 2, row['G'], ML_COEF)\n", "        predictions.append(pred)\n", "    \n", "    # Output results\n", "    # print(f\"\\nOptimization results for Instrument {instrument_number}:\")\n", "    # for name, val in ML_COEF.items():\n", "    #     print(f\"{name}: {val:.6f},\")\n", "    # # print(f\"Minimum MSE: {result.fun:.6f}\")\n", "    \n", "    # Compare predictions with true values\n", "    result_df = pd.DataFrame({\n", "        '区间': instrument_data['range'],\n", "        'G': instrument_data['G'],\n", "        'B': instrument_data['B'],\n", "        '预测R': np.round(predictions, 3),\n", "        '真实R': instrument_data['A_R']\n", "    })\n", "    print(\"\\nPrediction results comparison:\")\n", "    print(result_df.to_string(index=False))\n", "    print(f\"Overall prediction variance: {np.var(predictions):.4f}\")\n", "    \n", "    # Calculate variance per range\n", "    print(\"\\nVariance per range:\")\n", "    for r in ['0-2', '2-4', '4-6', '6-8']:\n", "        subset = result_df[result_df['区间'] == r]\n", "        print(f\"{r} range variance: {subset['预测R'].var():.4f}\")\n", "\n", "# Run optimization for Instrument 1\n", "optimize_instrument(1)\n", "\n", "# Run optimization for Instrument 3\n", "# optimize_instrument(3)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["仪器1机器学习"]}, {"cell_type": "code", "execution_count": 24, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "Optimization results for Instrument 1:\n", "L1: -0.976288,\n", "L2: 0.431122,\n", "L3: -0.344445,\n", "L4: 0.099233,\n", "scale: 37.465506,\n", "bias: 4.789724,\n", "Minimum MSE: 0.085771\n", "\n", "Prediction results comparison:\n", "  区间    G   B   预测R   真实R\n", " 0-2 1370 886 3.047 2.820\n", " 0-2 1365 866 3.404 3.040\n", " 0-2 1304 840 3.844 3.160\n", " 0-2 1341 863 3.445 3.260\n", " 0-2 1324 860 3.489 3.400\n", " 0-2 1310 837 3.904 3.170\n", " 0-2 1360 898 2.831 3.580\n", " 0-2 1371 884 3.084 3.400\n", " 0-2 1335 871 3.297 3.190\n", " 0-2 1340 871 3.299 3.300\n", " 2-4 1236 788 4.788 4.860\n", " 2-4 1274 815 4.294 4.690\n", " 2-4 1254 798 4.607 4.950\n", " 2-4 1216 793 4.674 4.760\n", " 2-4 1205 785 4.822 4.990\n", " 2-4 1170 780 4.891 4.820\n", " 2-4 1204 800 4.529 4.900\n", " 2-4 1223 804 4.467 4.750\n", " 2-4 1208 797 4.590 4.270\n", " 2-4 1249 802 4.525 4.570\n", " 4-6 1096 692 6.649 5.910\n", " 4-6 1166 721 6.095 6.230\n", " 4-6 1164 716 6.199 6.500\n", " 4-6 1174 716 6.209 5.930\n", " 4-6 1134 720 6.084 6.390\n", " 4-6 1063 691 6.633 6.360\n", " 4-6 1083 705 6.351 6.320\n", " 4-6 1100 714 6.176 6.450\n", " 4-6 1072 673 7.046 6.570\n", " 4-6 1098 713 6.195 6.580\n", " 6-8 1001 635 7.838 7.610\n", " 6-8  996 632 7.903 8.120\n", " 6-8  984 623 8.103 8.310\n", " 6-8  967 628 7.958 8.140\n", " 6-8  970 619 8.180 8.420\n", " 6-8  954 612 8.328 8.420\n", " 6-8  960 620 8.141 8.160\n", " 6-8  947 613 8.293 7.940\n", " 6-8  966 617 8.223 8.210\n", " 6-8  947 618 8.171 8.030\n", "8-10 1441 617 8.788 8.800\n", "8-10 1441 617 8.788 8.800\n", "8-10 1441 617 8.788 8.800\n", "8-10 1441 617 8.788 8.800\n", "8-10 1441 617 8.788 8.800\n", "8-10 1441 617 8.788 8.800\n", "8-10 1441 617 8.788 8.800\n", "8-10 1441 617 8.788 8.800\n", "8-10 1441 617 8.788 8.800\n", "8-10 1441 617 8.788 8.800\n", "Overall prediction variance: 4.2318\n", "\n", "Variance per range:\n", "0-2 range variance: 0.1131\n", "2-4 range variance: 0.0326\n", "4-6 range variance: 0.0983\n", "8-10 range variance: 0.0000\n"]}], "source": ["import math\n", "import pandas as pd\n", "import numpy as np\n", "from scipy.optimize import minimize\n", "\n", "# Data preprocessing function\n", "def prepare_data(data):\n", "    \"\"\"将分区间列重组为长格式\"\"\"\n", "    ranges = ['0-2', '2-4', '4-6', '6-8','8-10']\n", "    merged = []\n", "    \n", "    for r in ranges:\n", "        # Extract data for each range, ensuring no NaN values in '位置'\n", "        subset = data[data['位置'].str.contains(r, na=False)].copy()\n", "        subset['range'] = r\n", "        merged.append(subset)\n", "    \n", "    return pd.concat(merged).reset_index(drop=True)\n", "\n", "# Function to calculate the optimized Y value\n", "def calculate_Y_optimized(Blue, Dark, Green, ML_COEF):\n", "    \"\"\"使用机器学习优化后的预测函数\"\"\"\n", "    # Fixed parameters\n", "    g0 = 1095\n", "    b0 = 3048\n", "    g1 = 16\n", "    b1 = 48\n", "    d0 = 0\n", "    \n", "    try:\n", "        # Calculate intermediate variables\n", "        L1 = math.log10(Blue - Dark - b1) / math.log10(b0 - d0 - b1) + math.log10((Blue - Dark) / b0)\n", "        L2 = math.log10(Green - Dark - g1) / math.log10(g0 - d0 - g1) + math.log10((Green - Dark) / g0)\n", "        L3 = math.log10((Green - Dark)/(g0 - g1)) - math.log10((Blue - Dark)/(b0 - b1))\n", "        L4 = math.log10((Green - Dark)/(g0 - g1)) / math.log10((Blue - Dark)/(b0 - b1))\n", "        \n", "        # Apply optimized coefficients\n", "        X = (ML_COEF['L1'] * L1 + \n", "             ML_COEF['L2'] * L2 + \n", "             ML_COEF['L3'] * L3 + \n", "             ML_COEF['L4'] * L4)\n", "        \n", "        return ML_COEF['scale'] * X + ML_COEF['bias']\n", "    except:\n", "        return np.nan\n", "\n", "# Objective function for optimization\n", "def objective(params, instrument_data, target_column):\n", "    \"\"\"目标函数：最小化预测值与真实值的MSE\"\"\"\n", "    coef_L1, coef_L2, coef_L3, coef_L4, scale, bias = params\n", "    g0 = 1095\n", "    b0 = 3048\n", "    g1 = 16\n", "    b1 = 48\n", "    d0 = 0\n", "\n", "    predictions = []\n", "    for _, row in instrument_data.iterrows():\n", "        try:\n", "            # Use the raw measurement values from the selected instrument\n", "            Green = row['G']\n", "            Blue = row['B']\n", "            Dark = 2  # Fixed dark current value\n", "            \n", "            # Calculate intermediate variables\n", "            L1 = math.log10(Blue - Dark - b1) / math.log10(b0 - d0 - b1) + math.log10((Blue - Dark) / b0)\n", "            L2 = math.log10(Green - Dark - g1) / math.log10(g0 - d0 - g1) + math.log10((Green - Dark) / g0)\n", "            L3 = math.log10((Green - Dark)/(g0 - g1)) - math.log10((Blue - Dark)/(b0 - b1))\n", "            L4 = math.log10((Green - Dark)/(g0 - g1)) / math.log10((Blue - Dark)/(b0 - b1))\n", "            \n", "            # Use the coefficients to be optimized\n", "            X = coef_L1 * L1 + coef_L2 * L2 + coef_L3 * L3 + coef_L4 * L4\n", "            pred = scale * X + bias\n", "            predictions.append(pred)\n", "        except:\n", "            return np.inf\n", "    \n", "    mse = np.mean((np.array(predictions) - instrument_data[target_column])**2)\n", "    return mse\n", "\n", "# Load data\n", "data = pd.read_excel(\"2.27新探头对比1,3号.xlsx\")\n", "\n", "# Prepare data\n", "prepared_data = prepare_data(data)\n", "\n", "# Function to run optimization for a specific instrument\n", "def optimize_instrument(instrument_number):\n", "    # Select data for the specified instrument\n", "    if instrument_number == 1:\n", "        instrument_data = prepared_data[['range', '仪器1_G', '仪器1_B', '仪器A_R']].rename(columns={'仪器1_G': 'G', '仪器1_B': 'B', '仪器A_R': 'A_R'})\n", "    elif instrument_number == 3:\n", "        instrument_data = prepared_data[['range', '仪器3_G', '仪器3_B', '仪器A_R']].rename(columns={'仪器3_G': 'G', '仪器3_B': 'B', '仪器A_R': 'A_R'})\n", "    else:\n", "        raise ValueError(\"Invalid instrument number. Choose 1 or 3.\")\n", "    \n", "    # Define parameter bounds\n", "    bounds = [\n", "        (-2, 0),    # L1 coefficient\n", "        (-1, 1),    # L2 coefficient\n", "        (-1, 1),    # L3 coefficient\n", "        (-1, 1),    # L4 coefficient\n", "        (30, 45),   # Scale coefficient\n", "        (-10, 15)   # <PERSON>ias term\n", "    ]\n", "    \n", "    # Initial guess values\n", "    initial_guess = [-0.9975, 0.55, -0.202, -0.195, 38.105, 6.467]\n", "    \n", "    # Run optimization\n", "    result = minimize(\n", "        objective,\n", "        initial_guess,\n", "        args=(instrument_data, 'A_R'),\n", "        method='L-BFGS-B',\n", "        bounds=bounds,\n", "        options={'maxiter': 1000, 'disp': True}\n", "    )\n", "    \n", "    # Extract optimized parameters\n", "    ML_COEF = {\n", "        'L1': result.x[0],\n", "        'L2': result.x[1],\n", "        'L3': result.x[2],\n", "        'L4': result.x[3],\n", "        'scale': result.x[4],\n", "        'bias': result.x[5]\n", "    }\n", "    \n", "    # Validate the final results\n", "    predictions = []\n", "    for _, row in instrument_data.iterrows():\n", "        pred = calculate_Y_optimized(row['B'], 2, row['G'], ML_COEF)\n", "        predictions.append(pred)\n", "    \n", "    # Output results\n", "    print(f\"\\nOptimization results for Instrument {instrument_number}:\")\n", "    for name, val in ML_COEF.items():\n", "        print(f\"{name}: {val:.6f},\")\n", "    print(f\"Minimum MSE: {result.fun:.6f}\")\n", "    \n", "    # Compare predictions with true values\n", "    result_df = pd.DataFrame({\n", "        '区间': instrument_data['range'],\n", "        'G': instrument_data['G'],\n", "        'B': instrument_data['B'],\n", "        '预测R': np.round(predictions, 3),\n", "        '真实R': instrument_data['A_R']\n", "    })\n", "    print(\"\\nPrediction results comparison:\")\n", "    print(result_df.to_string(index=False))\n", "    print(f\"Overall prediction variance: {np.var(predictions):.4f}\")\n", "    \n", "    # Calculate variance per range\n", "    print(\"\\nVariance per range:\")\n", "    for r in ['0-2', '2-4', '4-6','8-10']:\n", "        subset = result_df[result_df['区间'] == r]\n", "        print(f\"{r} range variance: {subset['预测R'].var():.4f}\")\n", "\n", "# Run optimization for Instrument 1\n", "optimize_instrument(1)\n", "\n", "# Run optimization for Instrument 3\n", "# optimize_instrument(3)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["仪器3机器学习"]}, {"cell_type": "code", "execution_count": 17, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "Optimization results for Instrument 3:\n", "L1: -1.468751\n", "L2: 1.000000\n", "L3: -0.452713\n", "L4: 0.067797\n", "scale: 44.734406\n", "bias: -10.000000\n", "Minimum MSE: 0.182866\n", "\n", "Prediction results comparison:\n", " 区间     G     B   预测R  真实R\n", "0-2 846.0 812.0 3.400 2.82\n", "0-2 812.0 791.0 3.524 3.04\n", "0-2 807.0 777.0 3.936 3.16\n", "0-2 811.0 789.0 3.577 3.26\n", "0-2 790.0 772.0 3.796 3.40\n", "0-2 795.0 775.0 3.782 3.17\n", "0-2 819.0 791.0 3.653 3.58\n", "0-2 851.0 816.0 3.349 3.40\n", "0-2 832.0 805.0 3.394 3.19\n", "0-2 884.0 847.0 2.861 3.30\n", "2-4 866.0 766.0 5.409 4.86\n", "2-4 815.0 759.0 4.750 4.69\n", "2-4 781.0 737.0 4.936 4.95\n", "2-4 784.0 752.0 4.423 4.76\n", "2-4 750.0 729.0 4.627 4.99\n", "2-4 767.0 743.0 4.431 4.82\n", "2-4 795.0 750.0 4.711 4.90\n", "2-4 789.0 738.0 5.053 4.75\n", "2-4 813.0 758.0 4.750 4.27\n", "2-4 818.0 777.0 4.141 4.57\n", "4-6 727.0 680.0 6.120 5.91\n", "4-6 728.0 677.0 6.266 6.23\n", "4-6 750.0 701.0 5.736 6.50\n", "4-6 713.0 673.0 6.114 5.93\n", "4-6 709.0 679.0 5.776 6.39\n", "4-6 742.0 687.0 6.143 6.36\n", "4-6 752.0 694.0 6.061 6.32\n", "4-6 736.0 696.0 5.650 6.45\n", "4-6 749.0 706.0 5.514 6.57\n", "4-6 760.0 702.0 5.899 6.58\n", "6-8 591.0 574.0 7.707 7.61\n", "6-8 651.0 595.0 8.198 8.12\n", "6-8 592.0 561.0 8.383 8.31\n", "6-8 581.0 555.0 8.394 8.14\n", "6-8 584.0 561.0 8.169 8.42\n", "6-8 592.0 563.0 8.282 8.42\n", "6-8 564.0 541.0 8.652 8.16\n", "6-8 601.0 566.0 8.368 7.94\n", "6-8 595.0 564.0 8.311 8.21\n", "6-8 593.0 564.0 8.258 8.03\n", "Overall prediction variance: 3.1547\n", "\n", "Variance per range:\n", "0-2 range variance: 0.0925\n", "2-4 range variance: 0.1277\n", "4-6 range variance: 0.0621\n", "6-8 range variance: 0.0575\n"]}], "source": ["import math\n", "import pandas as pd\n", "import numpy as np\n", "from scipy.optimize import minimize\n", "\n", "# Data preprocessing function\n", "def prepare_data(data):\n", "    \"\"\"将分区间列重组为长格式\"\"\"\n", "    ranges = ['0-2', '2-4', '4-6', '6-8']\n", "    merged = []\n", "    \n", "    for r in ranges:\n", "        # Extract data for each range, ensuring no NaN values in '位置'\n", "        subset = data[data['位置'].str.contains(r, na=False)].copy()\n", "        subset['range'] = r\n", "        merged.append(subset)\n", "    \n", "    return pd.concat(merged).reset_index(drop=True)\n", "\n", "# Function to calculate the optimized Y value\n", "def calculate_Y_optimized(Blue, Dark, Green, ML_COEF):\n", "    \"\"\"使用机器学习优化后的预测函数\"\"\"\n", "    # Fixed parameters\n", "    g0 = 1000\n", "    b0 = 3000\n", "    g1 = 27\n", "    b1 = 53\n", "    d0 = 8\n", "    \n", "    try:\n", "        # Calculate intermediate variables\n", "        L1 = math.log10(Blue - Dark - b1) / math.log10(b0 - d0 - b1) + math.log10((Blue - Dark) / b0)\n", "        L2 = math.log10(Green - Dark - g1) / math.log10(g0 - d0 - g1) + math.log10((Green - Dark) / g0)\n", "        L3 = math.log10((Green - Dark)/(g0 - g1)) - math.log10((Blue - Dark)/(b0 - b1))\n", "        L4 = math.log10((Green - Dark)/(g0 - g1)) / math.log10((Blue - Dark)/(b0 - b1))\n", "        \n", "        # Apply optimized coefficients\n", "        X = (ML_COEF['L1'] * L1 + \n", "             ML_COEF['L2'] * L2 + \n", "             ML_COEF['L3'] * L3 + \n", "             ML_COEF['L4'] * L4)\n", "        \n", "        return ML_COEF['scale'] * X + ML_COEF['bias']\n", "    except:\n", "        return np.nan\n", "\n", "# Objective function for optimization\n", "def objective(params, instrument_data, target_column):\n", "    \"\"\"目标函数：最小化预测值与真实值的MSE\"\"\"\n", "    coef_L1, coef_L2, coef_L3, coef_L4, scale, bias = params\n", "    g0 = 1000\n", "    b0 = 3000\n", "    g1 = 27\n", "    b1 = 53\n", "    d0 = 8\n", "\n", "    predictions = []\n", "    for _, row in instrument_data.iterrows():\n", "        try:\n", "            # Use the raw measurement values from the selected instrument\n", "            Green = row['G']\n", "            Blue = row['B']\n", "            Dark = 2  # Fixed dark current value\n", "            \n", "            # Calculate intermediate variables\n", "            L1 = math.log10(Blue - Dark - b1) / math.log10(b0 - d0 - b1) + math.log10((Blue - Dark) / b0)\n", "            L2 = math.log10(Green - Dark - g1) / math.log10(g0 - d0 - g1) + math.log10((Green - Dark) / g0)\n", "            L3 = math.log10((Green - Dark)/(g0 - g1)) - math.log10((Blue - Dark)/(b0 - b1))\n", "            L4 = math.log10((Green - Dark)/(g0 - g1)) / math.log10((Blue - Dark)/(b0 - b1))\n", "            \n", "            # Use the coefficients to be optimized\n", "            X = coef_L1 * L1 + coef_L2 * L2 + coef_L3 * L3 + coef_L4 * L4\n", "            pred = scale * X + bias\n", "            predictions.append(pred)\n", "        except:\n", "            return np.inf\n", "    \n", "    mse = np.mean((np.array(predictions) - instrument_data[target_column])**2)\n", "    return mse\n", "\n", "# Load data\n", "data = pd.read_excel(\"2.27新探头对比1,3号.xlsx\")\n", "\n", "# Prepare data\n", "prepared_data = prepare_data(data)\n", "\n", "# Function to run optimization for a specific instrument\n", "def optimize_instrument(instrument_number):\n", "    # Select data for the specified instrument\n", "    if instrument_number == 1:\n", "        instrument_data = prepared_data[['range', '仪器1_G', '仪器1_B', '仪器A_R']].rename(columns={'仪器1_G': 'G', '仪器1_B': 'B', '仪器A_R': 'A_R'})\n", "    elif instrument_number == 3:\n", "        instrument_data = prepared_data[['range', '仪器3_G', '仪器3_B', '仪器A_R']].rename(columns={'仪器3_G': 'G', '仪器3_B': 'B', '仪器A_R': 'A_R'})\n", "    else:\n", "        raise ValueError(\"Invalid instrument number. Choose 1 or 3.\")\n", "    \n", "    # Define parameter bounds\n", "    bounds = [\n", "        (-2, 0),    # L1 coefficient\n", "        (-1, 1),    # L2 coefficient\n", "        (-1, 1),    # L3 coefficient\n", "        (-1, 1),    # L4 coefficient\n", "        (30, 45),   # Scale coefficient\n", "        (-10, 15)   # <PERSON>ias term\n", "    ]\n", "    \n", "    # Initial guess values\n", "    initial_guess = [-0.9975, 0.55, -0.202, -0.195, 38.105, 6.467]\n", "    \n", "    # Run optimization\n", "    result = minimize(\n", "        objective,\n", "        initial_guess,\n", "        args=(instrument_data, 'A_R'),\n", "        method='L-BFGS-B',\n", "        bounds=bounds,\n", "        options={'maxiter': 1000, 'disp': True}\n", "    )\n", "    \n", "    # Extract optimized parameters\n", "    ML_COEF = {\n", "        'L1': result.x[0],\n", "        'L2': result.x[1],\n", "        'L3': result.x[2],\n", "        'L4': result.x[3],\n", "        'scale': result.x[4],\n", "        'bias': result.x[5]\n", "    }\n", "    \n", "    # Validate the final results\n", "    predictions = []\n", "    for _, row in instrument_data.iterrows():\n", "        pred = calculate_Y_optimized(row['B'], 2, row['G'], ML_COEF)\n", "        predictions.append(pred)\n", "    \n", "    # Output results\n", "    print(f\"\\nOptimization results for Instrument {instrument_number}:\")\n", "    for name, val in ML_COEF.items():\n", "        print(f\"{name}: {val:.6f}\")\n", "    print(f\"Minimum MSE: {result.fun:.6f}\")\n", "    \n", "    # Compare predictions with true values\n", "    result_df = pd.DataFrame({\n", "        '区间': instrument_data['range'],\n", "        'G': instrument_data['G'],\n", "        'B': instrument_data['B'],\n", "        '预测R': np.round(predictions, 3),\n", "        '真实R': instrument_data['A_R']\n", "    })\n", "    print(\"\\nPrediction results comparison:\")\n", "    print(result_df.to_string(index=False))\n", "    print(f\"Overall prediction variance: {np.var(predictions):.4f}\")\n", "    \n", "    # Calculate variance per range\n", "    print(\"\\nVariance per range:\")\n", "    for r in ['0-2', '2-4', '4-6', '6-8']:\n", "        subset = result_df[result_df['区间'] == r]\n", "        print(f\"{r} range variance: {subset['预测R'].var():.4f}\")\n", "\n", "# Run optimization for Instrument 1\n", "# optimize_instrument(1)\n", "\n", "# Run optimization for Instrument 3\n", "optimize_instrument(3)"]}, {"cell_type": "markdown", "metadata": {}, "source": []}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "优化结果：\n", "L1系数: -0.111426\n", "L2系数: -0.317549\n", "L3系数: 1.000000\n", "L4系数: 0.371668\n", "比例系数: 37.789418\n", "偏置项: -10.000000\n", "最小MSE: 0.196308\n", "\n", "预测结果对比：\n", " 区间  仪器4_G  仪器4_B   预测R  真实R\n", "0-2    802    641 2.286 1.95\n", "0-2    793    662 1.855 1.77\n", "0-2    769    654 2.007 1.82\n", "0-2    765    667 1.752 1.86\n", "0-2    747    644 2.199 2.08\n", "0-2    764    648 2.126 1.13\n", "0-2    745    645 2.178 2.22\n", "0-2    766    646 2.167 2.46\n", "0-2    744    626 2.567 2.20\n", "0-2    755    645 2.182 2.23\n", "2-4    722    551 4.267 3.71\n", "2-4    750    571 3.816 3.82\n", "2-4    680    542 4.419 3.98\n", "2-4    700    559 4.033 4.16\n", "2-4    681    549 4.246 3.78\n", "2-4    702    560 4.012 3.99\n", "2-4    682    542 4.423 3.85\n", "2-4    690    549 4.262 4.49\n", "2-4    678    546 4.315 4.61\n", "2-4    697    555 4.126 4.03\n", "4-6    674    527 4.793 5.38\n", "4-6    649    516 5.028 5.47\n", "4-6    645    519 4.939 5.42\n", "4-6    650    522 4.872 5.38\n", "4-6    646    520 4.915 5.72\n", "4-6    633    504 5.313 5.74\n", "4-6    652    519 4.955 5.58\n", "4-6    609    502 5.304 5.17\n", "4-6    640    510 5.167 5.85\n", "4-6    641    506 5.278 5.82\n", "6-8    576    432 7.288 7.24\n", "6-8    515    410 7.766 6.79\n", "6-8    531    424 7.367 7.09\n", "6-8    527    410 7.822 7.31\n", "6-8    542    411 7.855 7.15\n", "6-8    524    412 7.739 8.17\n", "6-8    518    406 7.918 7.80\n", "6-8    523    410 7.803 7.79\n", "6-8    521    416 7.589 7.47\n", "6-8    514    412 7.692 8.15\n", "总体预测方差: 4.0051\n", "\n", "分区间方差：\n", "0-2区间方差: 0.0511\n", "2-4区间方差: 0.0374\n", "4-6区间方差: 0.0374\n", "6-8区间方差: 0.0437\n"]}], "source": ["import math\n", "import pandas as pd\n", "import numpy as np\n", "from scipy.optimize import minimize\n", "\n", "# 数据预处理函数\n", "def prepare_data(data):\n", "    \"\"\"将分区间列重组为长格式\"\"\"\n", "    ranges = ['0-2', '2-4', '4-6', '6-8']\n", "    merged = []\n", "    \n", "    for r in ranges:\n", "        # 提取每个区间的数据\n", "        subset = data[[\n", "            f'仪器A_G({r})', f'仪器A_B({r})', f'仪器A_R({r})',\n", "            f'仪器4_G({r})', f'仪器4_B({r})'\n", "        ]].dropna()\n", "        \n", "        # 重命名列\n", "        subset.columns = ['A_G', 'A_B', 'A_R', 'G4', 'B4']\n", "        subset['range'] = r\n", "        merged.append(subset)\n", "    \n", "    return pd.concat(merged)\n", "\n", "# 加载数据\n", "data = pd.read_excel(\"2.24新探头大范围.xlsx\")\n", "comparison_data = prepare_data(data)\n", "\n", "def objective(params):\n", "    \"\"\"目标函数：最小化预测值与真实值的MSE\"\"\"\n", "    coef_L1, coef_L2, coef_L3, coef_L4, scale, bias = params\n", "    \n", "    predictions = []\n", "    for _, row in comparison_data.iterrows():\n", "        try:\n", "            # 直接使用仪器4的原始测量值\n", "            Green = row['G4']\n", "            Blue = row['B4']\n", "            Dark = 2  # 固定暗电流值\n", "\n", "            # 计算中间变量\n", "            L1 = math.log10(Blue - Dark - 53) / math.log10(3000 - 8 - 53) + math.log10((Blue - Dark) / 3000)\n", "            L2 = math.log10(Green - Dark - 27) / math.log10(2000 - 8 - 27) + math.log10((Green - Dark) / 2000)\n", "            L3 = math.log10((Green - Dark)/(2000 - 27)) - math.log10((Blue - Dark)/(3000 - 53))\n", "            L4 = math.log10((Green - Dark)/(2000 - 27)) / math.log10((Blue - Dark)/(3000 - 53))\n", "\n", "            # 使用待优化的系数\n", "            X = coef_L1*L1 + coef_L2*L2 + coef_L3*L3 + coef_L4*L4\n", "            pred = scale * X + bias\n", "            predictions.append(pred)\n", "        except:\n", "            return np.inf\n", "    \n", "    mse = np.mean((np.array(predictions) - comparison_data['A_R'])**2)\n", "    return mse\n", "\n", "# 参数设置（根据经验调整边界）\n", "bounds = [\n", "    (-2, 0),    # L1系数（原-0.9975）\n", "    (-1, 1),     # L2系数（原0.55）\n", "    (-1, 1),    # L3系数（原-0.202）\n", "    (-1, 1),    # L4系数（原-0.195）\n", "    (30, 45),   # 比例系数（原38.105）\n", "    (-10, 15)     # 偏置项（原6.467）\n", "]\n", "\n", "# 初始猜测值（基于原始参数）\n", "initial_guess = [-0.9975, 0.55, -0.202, -0.195, 38.105, 6.467]\n", "\n", "# 执行优化\n", "result = minimize(\n", "    objective,\n", "    initial_guess,\n", "    method='L-BFGS-B',\n", "    bounds=bounds,\n", "    options={'maxiter': 1000, 'disp': True}\n", ")\n", "\n", "# 输出结果\n", "print(\"\\n优化结果：\")\n", "params_names = ['L1系数', 'L2系数', 'L3系数', 'L4系数', '比例系数', '偏置项']\n", "for name, val in zip(params_names, result.x):\n", "    print(f\"{name}: {val:.6f}\")\n", "print(f\"最小MSE: {result.fun:.6f}\")\n", "\n", "# 验证最终效果\n", "final_params = result.x\n", "predictions = []\n", "for _, row in comparison_data.iterrows():\n", "    Green = row['G4']\n", "    Blue = row['B4']\n", "    Dark = 2\n", "    \n", "    L1 = math.log10(Blue - Dark - 53) / math.log10(3000 - 8 - 53) + math.log10((Blue - Dark) / 3000)\n", "    L2 = math.log10(Green - Dark - 27) / math.log10(2000 - 8 - 27) + math.log10((Green - Dark) / 2000)\n", "    L3 = math.log10((Green - Dark)/(2000 - 27)) - math.log10((Blue - Dark)/(3000 - 53))\n", "    L4 = math.log10((Green - Dark)/(2000 - 27)) / math.log10((Blue - Dark)/(3000 - 53))\n", "    \n", "    X = final_params[0]*L1 + final_params[1]*L2 + final_params[2]*L3 + final_params[3]*L4\n", "    pred = final_params[4] * X + final_params[5]\n", "    predictions.append(pred)\n", "\n", "# 输出预测结果\n", "print(\"\\n预测结果对比：\")\n", "result_df = pd.DataFrame({\n", "    '区间': comparison_data['range'],\n", "    '仪器4_G': comparison_data['G4'],\n", "    '仪器4_B': comparison_data['B4'],\n", "    '预测R': np.round(predictions, 3),\n", "    '真实R': comparison_data['A_R']\n", "})\n", "print(result_df.to_string(index=False))\n", "print(f\"总体预测方差: {np.var(predictions):.4f}\")\n", "\n", "# 按区间显示方差\n", "print(\"\\n分区间方差：\")\n", "for r in ['0-2', '2-4', '4-6', '6-8']:\n", "    subset = result_df[result_df['区间'] == r]\n", "    print(f\"{r}区间方差: {subset['预测R'].var():.4f}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "================================================== 仪器4预测结果（0-2区间） ==================================================\n", "仪器4预测结果（0-2区间）：\n", " 仪器4_G(0-2)  仪器4_B(0-2)   预测的R\n", "        802         641 -0.335\n", "        793         662 -1.245\n", "        769         654 -1.471\n", "        765         667 -2.004\n", "        747         644 -1.596\n", "        764         648 -1.368\n", "        745         645 -1.675\n", "        766         646 -1.256\n", "        744         626 -1.019\n", "        755         645 -1.457\n", "仪器4预测方差（0-2区间）: 0.19773470480122624\n", "\n", "================================================== 仪器4预测结果（2-4区间） ==================================================\n", "仪器4预测结果（2-4区间）：\n", " 仪器4_G(2-4)  仪器4_B(2-4)  预测的R\n", "        722         551 1.353\n", "        750         571 1.169\n", "        680         542 0.758\n", "        700         559 0.536\n", "        681         549 0.496\n", "        702         560 0.542\n", "        682         542 0.806\n", "        690         549 0.707\n", "        678         546 0.547\n", "        697         555 0.627\n", "仪器4预测方差（2-4区间）: 0.0837355078155016\n", "\n", "================================================== 仪器4预测结果（4-6区间） ==================================================\n", "仪器4预测结果（4-6区间）：\n", " 仪器4_G(4-6)  仪器4_B(4-6)  预测的R\n", "        674         527 1.240\n", "        649         516 1.101\n", "        645         519 0.872\n", "        650         522 0.868\n", "        646         520 0.854\n", "        633         504 1.221\n", "        652         519 1.046\n", "        609         502 0.682\n", "        640         510 1.135\n", "        641         506 1.336\n", "仪器4预测方差（4-6区间）: 0.04369439490842751\n", "\n", "================================================== 仪器4预测结果（6-8区间） ==================================================\n", "仪器4预测结果（6-8区间）：\n", " 仪器4_G(6-8)  仪器4_B(6-8)  预测的R\n", "        576         432 3.107\n", "        515         410 2.432\n", "        531         424 2.193\n", "        527         410 2.812\n", "        542         411 3.218\n", "        524         412 2.610\n", "        518         406 2.745\n", "        523         410 2.686\n", "        521         416 2.301\n", "        514         412 2.292\n", "仪器4预测方差（6-8区间）: 0.11935855536478754\n"]}], "source": ["#调参预测 输入拟合（k_g,k_b）\n", "import math\n", "import pandas as pd\n", "import os\n", "\n", "os.chdir(\"D:\\E3A\\亿杉算法实习\\温度补偿\\新探头_亿光led\")#有数字需要双反斜杠\n", "\n", "\n", "# 设置显示选项\n", "pd.set_option('display.max_rows', None)\n", "pd.set_option('display.max_columns', None)\n", "pd.set_option('display.float_format', '{:.3f}'.format)\n", "\n", "def calculate_Y0(<PERSON>, <PERSON>, <PERSON>):\n", "    \"\"\"旧仪器预测函数\"\"\"\n", "    d0 = 6\n", "    g0 = 1046\n", "    b0 = 3010\n", "    d1 = 7\n", "    g1 = 27\n", "    b1 = 53\n", "\n", "    L1 = math.log10(Blue - Dark - b1) / math.log10(b0 - d0 - b1) + math.log10((Blue - Dark) / b0)\n", "    L2 = math.log10(Green - Dark - g1) / math.log10(g0 - d0 - g1) + math.log10((Green - Dark) / g0)\n", "    L3 = math.log10((Green - Dark)/(g0 - g1)) - math.log10((Blue - Dark)/(b0 - b1))\n", "    L4 = math.log10((Green - Dark)/(g0 - g1)) / math.log10((Blue - Dark)/(b0 - b1))\n", "\n", "    X = -0.9975 * L1 + 0.55 * L2 - 0.202 * L3 - 0.195 * L4\n", "    return 38.105 * X + 6.467\n", "\n", "def calculate_Y(<PERSON>, <PERSON>, Green):\n", "    \"\"\"新仪器预测函数\"\"\" \n", "    g0_real =2000 #实际校准值应该是2000\n", "    d0 = 8\n", "    g0 = 1000 #算法使用值和旧仪器一致\n", "    b0 = 3000\n", "    d1 = 10\n", "    g1 = 27\n", "    b1 = 53\n", "\n", "    L1 = math.log10(Blue - Dark - b1) / math.log10(b0 - d0 - b1) + math.log10((Blue - Dark) / b0)\n", "    L2 = math.log10(Green - Dark - g1) / math.log10(g0 - d0 - g1) + math.log10((Green - Dark) / g0)\n", "    L3 = math.log10((Green - Dark)/(g0 - g1)) - math.log10((Blue - Dark)/(b0 - b1))\n", "    L4 = math.log10((Green - Dark)/(g0 - g1)) / math.log10((Blue - Dark)/(b0 - b1))\n", "\n", "    X = -0.9975 * L1 + 0.55 * L2 - 0.202 * L3 - 0.195 * L4\n", "    return 38.105 * X + 6.467\n", "\n", "# 加载数据\n", "file_path = \"2.24新探头大范围.xlsx\"\n", "data = pd.read_excel(file_path)\n", "\n", "k_green = 0.4\n", "k_blue = 1.01\n", "k_base = 100.0076\n", "\n", "# -------------------------- 新仪器3预测 --------------------------\n", "# print(\"\\n\" + \"=\"*50 + \" 仪器3预测结果 \" + \"=\"*50)\n", "# instrument3_data = data[['仪器3_G', '仪器3_B']].dropna().copy()\n", "\n", "# instrument3_data['预测的R'] = instrument3_data.apply(\n", "#     lambda row: calculate_Y(\n", "#         Blue=row['仪器3_B']*k_blue,  # 调整Blue通道\n", "#         Dark=2,\n", "#         Green=(row['仪器3_G']- k_base)*k_green  # 调整Green通道\n", "#     ), \n", "#     axis=1\n", "# )\n", "\n", "# print(\"仪器3预测结果：\")\n", "# print(instrument3_data.round(3).to_string(index=False))\n", "# print(\"仪器3预测方差:\", instrument3_data['预测的R'].var())\n", "\n", "# #-------------------------- 新仪器4预测 --------------------------\n", "# 定义区间列表\n", "ranges = ['0-2', '2-4', '4-6', '6-8']\n", "\n", "# 对每个区间进行预测\n", "for r in ranges:\n", "    # 仪器4\n", "    print(\"\\n\" + \"=\"*50 + f\" 仪器4预测结果（{r}区间） \" + \"=\"*50)\n", "    \n", "    # 提取当前区间的数据\n", "    instrument4_data = data[[f'仪器4_G({r})', f'仪器4_B({r})']].dropna().copy()\n", "    \n", "    # 进行预测\n", "    instrument4_data['预测的R'] = instrument4_data.apply(\n", "        lambda row: calculate_Y(\n", "            Blue=row[f'仪器4_B({r})'] * k_blue,  # 调整Blue通道\n", "            Dark=2,\n", "            Green=(row[f'仪器4_G({r})'] - k_base) * k_green  # 调整Green通道\n", "        ), \n", "        axis=1\n", "    )\n", "    \n", "    # 输出结果\n", "    print(f\"仪器4预测结果（{r}区间）：\")\n", "    print(instrument4_data.round(3).to_string(index=False))\n", "    print(f\"仪器4预测方差（{r}区间）:\", instrument4_data['预测的R'].var())\n", "\n", "    #仪器A\n", "\n", "    # print(\"\\n\" + \"=\"*50 + f\" 仪器A预测结果（{r}区间） \" + \"=\"*50)\n", "    \n", "    # # 提取当前区间的数据\n", "    # instrumentA_data = data[[f'仪器A_G({r})', f'仪器A_B({r})']].dropna().copy()\n", "    \n", "    # # 进行预测\n", "    # instrumentA_data['预测的R'] = instrumentA_data.apply(\n", "    #     lambda row: calculate_Y(\n", "    #         Blue= row[f'仪器A_B({r})'],   # 调整Blue通道\n", "    #         Dark=2,\n", "    #         Green= row[f'仪器A_G({r})'] # 调整Green通道\n", "    #     ), \n", "    #     axis=1\n", "    # )\n", "    \n", "    # # 输出结果\n", "    # print(f\"仪器A预测结果（{r}区间）：\")\n", "    # print(instrumentA_data.round(3).to_string(index=False))\n", "    # print(f\"仪器A预测方差（{r}区间）:\", instrumentA_data['预测的R'].var())"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "================================================== 仪器4预测结果（0-2区间） ==================================================\n", "仪器4预测结果（0-2区间）：\n", " 仪器4_G(0-2)  仪器4_B(0-2)  预测的R\n", "        802         641 5.945\n", "        793         662 5.093\n", "        769         654 4.876\n", "        765         667 4.377\n", "        747         644 4.754\n", "        764         648 4.970\n", "        745         645 4.680\n", "        766         646 5.076\n", "        744         626 5.294\n", "        755         645 4.886\n", "仪器4预测方差（0-2区间）: 0.17500179505116617\n", "\n", "================================================== 仪器4预测结果（2-4区间） ==================================================\n", "仪器4预测结果（2-4区间）：\n", " 仪器4_G(2-4)  仪器4_B(2-4)  预测的R\n", "        722         551 7.526\n", "        750         571 7.351\n", "        680         542 6.970\n", "        700         559 6.757\n", "        681         549 6.721\n", "        702         560 6.762\n", "        682         542 7.014\n", "        690         549 6.920\n", "        678         546 6.770\n", "        697         555 6.843\n", "仪器4预测方差（2-4区间）: 0.07382639414906306\n", "\n", "================================================== 仪器4预测结果（4-6区间） ==================================================\n", "仪器4预测结果（4-6区间）：\n", " 仪器4_G(4-6)  仪器4_B(4-6)  预测的R\n", "        674         527 7.428\n", "        649         516 7.304\n", "        645         519 7.088\n", "        650         522 7.083\n", "        646         520 7.071\n", "        633         504 7.425\n", "        652         519 7.251\n", "        609         502 6.926\n", "        640         510 7.341\n", "        641         506 7.530\n", "仪器4预测方差（4-6区间）: 0.0380442046239734\n", "\n", "================================================== 仪器4预测结果（6-8区间） ==================================================\n", "仪器4预测结果（6-8区间）：\n", " 仪器4_G(6-8)  仪器4_B(6-8)  预测的R\n", "        576         432 9.263\n", "        515         410 8.692\n", "        531         424 8.441\n", "        527         410 9.038\n", "        542         411 9.407\n", "        524         412 8.850\n", "        518         406 8.987\n", "        523         410 8.924\n", "        521         416 8.558\n", "        514         412 8.559\n", "仪器4预测方差（6-8区间）: 0.09985275666016667\n"]}], "source": ["#调参预测\n", "import math\n", "import pandas as pd\n", "import os\n", "\n", "os.chdir(\"D:\\E3A\\亿杉算法实习\\温度补偿\\新探头_亿光led\")#有数字需要双反斜杠\n", "\n", "\n", "# 设置显示选项\n", "pd.set_option('display.max_rows', None)\n", "pd.set_option('display.max_columns', None)\n", "pd.set_option('display.float_format', '{:.3f}'.format)\n", "\n", "def calculate_Y0(<PERSON>, <PERSON>, <PERSON>):\n", "    \"\"\"旧仪器预测函数\"\"\"\n", "    d0 = 6\n", "    g0 = 1046\n", "    b0 = 3010\n", "    d1 = 7\n", "    g1 = 27\n", "    b1 = 53\n", "\n", "    L1 = math.log10(Blue - Dark - b1) / math.log10(b0 - d0 - b1) + math.log10((Blue - Dark) / b0)\n", "    L2 = math.log10(Green - Dark - g1) / math.log10(g0 - d0 - g1) + math.log10((Green - Dark) / g0)\n", "    L3 = math.log10((Green - Dark)/(g0 - g1)) - math.log10((Blue - Dark)/(b0 - b1))\n", "    L4 = math.log10((Green - Dark)/(g0 - g1)) / math.log10((Blue - Dark)/(b0 - b1))\n", "\n", "    X = -0.9975 * L1 + 0.55 * L2 - 0.202 * L3 - 0.195 * L4\n", "    return 38.105 * X + 6.467\n", "\n", "def calculate_Y(<PERSON>, <PERSON>, Green):\n", "    \"\"\"新仪器预测函数\"\"\" \n", "    g0_real =2000 #实际校准值应该是2000\n", "    d0 = 8\n", "    g0 = 1000 #算法使用值和旧仪器一致\n", "    b0 = 3000\n", "    d1 = 10\n", "    g1 = 27\n", "    b1 = 53\n", "\n", "    L1 = math.log10(Blue - Dark - b1) / math.log10(b0 - d0 - b1) + math.log10((Blue - Dark) / b0)\n", "    L2 = math.log10(Green - Dark - g1) / math.log10(g0 - d0 - g1) + math.log10((Green - Dark) / g0)\n", "    L3 = math.log10((Green - Dark)/(g0 - g1)) - math.log10((Blue - Dark)/(b0 - b1))\n", "    L4 = math.log10((Green - Dark)/(g0 - g1)) / math.log10((Blue - Dark)/(b0 - b1))\n", "\n", "    X = -0.9975 * L1 + 0.55 * L2 - 0.202 * L3 - 0.195 * L4\n", "    return 38.105 * X + 6.467\n", "\n", "# 加载数据\n", "file_path = \"2.24新探头大范围.xlsx\"\n", "data = pd.read_excel(file_path)\n", "\n", "k_green = 0.6\n", "k_blue = 1.01\n", "k_base = 70.0076\n", "\n", "# -------------------------- 新仪器3预测 --------------------------\n", "# print(\"\\n\" + \"=\"*50 + \" 仪器3预测结果 \" + \"=\"*50)\n", "# instrument3_data = data[['仪器3_G', '仪器3_B']].dropna().copy()\n", "\n", "# instrument3_data['预测的R'] = instrument3_data.apply(\n", "#     lambda row: calculate_Y(\n", "#         Blue=row['仪器3_B']*k_blue,  # 调整Blue通道\n", "#         Dark=2,\n", "#         Green=(row['仪器3_G']- k_base)*k_green  # 调整Green通道\n", "#     ), \n", "#     axis=1\n", "# )\n", "\n", "# print(\"仪器3预测结果：\")\n", "# print(instrument3_data.round(3).to_string(index=False))\n", "# print(\"仪器3预测方差:\", instrument3_data['预测的R'].var())\n", "\n", "# #-------------------------- 新仪器4预测 --------------------------\n", "# 定义区间列表\n", "ranges = ['0-2', '2-4', '4-6', '6-8']\n", "\n", "# 对每个区间进行预测\n", "for r in ranges:\n", "    #仪器4\n", "    print(\"\\n\" + \"=\"*50 + f\" 仪器4预测结果（{r}区间） \" + \"=\"*50)\n", "    \n", "    # 提取当前区间的数据\n", "    instrument4_data = data[[f'仪器4_G({r})', f'仪器4_B({r})']].dropna().copy()\n", "    \n", "    # 进行预测\n", "    instrument4_data['预测的R'] = instrument4_data.apply(\n", "        lambda row: calculate_Y(\n", "            Blue=row[f'仪器4_B({r})'] * k_blue,  # 调整Blue通道\n", "            Dark=2,\n", "            Green=(row[f'仪器4_G({r})'] - k_base) * k_green  # 调整Green通道\n", "        ), \n", "        axis=1\n", "    )\n", "    \n", "    # 输出结果\n", "    print(f\"仪器4预测结果（{r}区间）：\")\n", "    print(instrument4_data.round(3).to_string(index=False))\n", "    print(f\"仪器4预测方差（{r}区间）:\", instrument4_data['预测的R'].var())\n", "\n", "    #仪器A\n", "\n", "    # print(\"\\n\" + \"=\"*50 + f\" 仪器A预测结果（{r}区间） \" + \"=\"*50)\n", "    \n", "    # # 提取当前区间的数据\n", "    # instrumentA_data = data[[f'仪器A_G({r})', f'仪器A_B({r})']].dropna().copy()\n", "    \n", "    # # 进行预测\n", "    # instrumentA_data['预测的R'] = instrumentA_data.apply(\n", "    #     lambda row: calculate_Y(\n", "    #         Blue=row[f'仪器4_B({r})'] * k_blue,  # 调整Blue通道\n", "    #         Dark=2,\n", "    #         Green=(row[f'仪器4_G({r})'] - k_base) * k_green  # 调整Green通道\n", "    #     ), \n", "    #     axis=1\n", "    # )\n", "    \n", "    # # 输出结果\n", "    # print(f\"仪器A预测结果（{r}区间）：\")\n", "    # print(instrumentA_data.round(3).to_string(index=False))\n", "    # print(f\"仪器A预测方差（{r}区间）:\", instrumentA_data['预测的R'].var())"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def calculate_Y(<PERSON>, <PERSON>, Green):\n", "    \"\"\"新仪器预测函数\"\"\"\n", "    # 核心参数定义\n", "    g0_real = 2000    # 实际校准基准值（与旧仪器不同）\n", "    d0, g0, b0 = 8, 1000, 3000  # 算法内部基准参数（与旧仪器一致）\n", "    d1, g1, b1 = 10, 27, 53      # 非线性修正参数\n", "    \n", "    # 信号处理公式\n", "    L1 = log10(Blue - Dark - b1) / log10(b0 - d0 - b1) + log10((Blue - Dark)/b0)\n", "    L2 = log10(Green - Dark - g1) / log10(g0 - d0 - g1) + log10((Green - Dark)/g0)\n", "    L3 = log10((Green-Dark)/(g0-g1)) - log10((Blue-Dark)/(b0-b1))\n", "    L4 = log10((Green-Dark)/(g0-g1)) / log10((Blue-Dark)/(b0-b1))\n", "    \n", "    # 线性组合预测\n", "    X = -0.9975*L1 + 0.55*L2 - 0.202*L3 - 0.195*L4\n", "    return 38.105*X + 6.467  # 最终预测值\n", "\n", "\n", "Blue = Blue  * k_blue,   # 缩放蓝色信号\n", "Green=(Green - k_base) * k_green  # 先扣除基线再缩放绿色信号\n"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.11"}}, "nbformat": 4, "nbformat_minor": 2}