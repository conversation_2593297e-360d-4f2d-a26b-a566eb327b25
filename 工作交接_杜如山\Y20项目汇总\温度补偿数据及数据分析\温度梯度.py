import pandas as pd
import matplotlib.pyplot as plt

# 读取Excel文件
df = pd.read_excel('1.10温度梯度重置表格.xlsx')

# 提取温度、6个仪器的G值、B值和R值数据
temperatures = df['温度']
g_values = df[['仪器1_G', '仪器2_G', '仪器3_G', '仪器4_G', '仪器5_G', '仪器6_G']]
b_values = df[['仪器1_B', '仪器2_B', '仪器3_B', '仪器4_B', '仪器5_B', '仪器6_B']]
r_values = df[['仪器1_R', '仪器2_R', '仪器3_R', '仪器4_R', '仪器5_R', '仪器6_R']]

# 创建一个新的DataFrame
data = pd.DataFrame({
    'Temperature': temperatures,
    'G_仪器1_G': g_values['仪器1_G'],
    'G_仪器2_G': g_values['仪器2_G'],
    'G_仪器3_G': g_values['仪器3_G'],
    'G_仪器4_G': g_values['仪器4_G'],
    'G_仪器5_G': g_values['仪器5_G'],
    'G_仪器6_G': g_values['仪器6_G'],
    'B_仪器1_B': b_values['仪器1_B'],
    'B_仪器2_B': b_values['仪器2_B'],
    'B_仪器3_B': b_values['仪器3_B'],
    'B_仪器4_B': b_values['仪器4_B'],
    'B_仪器5_B': b_values['仪器5_B'],
    'B_仪器6_B': b_values['仪器6_B'],
    'R_仪器1_R': r_values['仪器1_R'],
    'R_仪器2_R': r_values['仪器2_R'],
    'R_仪器3_R': r_values['仪器3_R'],
    'R_仪器4_R': r_values['仪器4_R'],
    'R_仪器5_R': r_values['仪器5_R'],
    'R_仪器6_R': r_values['仪器6_R']
})

# 对相同温度的G值、B值和R值进行分组并计算平均值
grouped_data = data.groupby('Temperature').mean().reset_index()

# 提取平均温度
avg_temperatures = grouped_data['Temperature']

# 定义颜色和标记
colors = ['blue', 'green', 'red', 'cyan', 'magenta', 'yellow']
markers = ['o', 's', '^', 'v', '>', '<']

# 绘制G值与温度的关系图
plt.figure(figsize=(10, 6))
for i in range(1, 7):
    plt.plot(avg_temperatures, grouped_data[f'G_仪器{i}_G'], color=colors[i-1], marker=markers[i-1], label=f'Instrument {i}')
plt.title('Relationship between Temperature and Average G Values of Instruments')
plt.xlabel('Temperature (°C)')
plt.ylabel('Average G Value')
plt.legend()
plt.grid(True)
plt.show()

# 绘制B值与温度的关系图
plt.figure(figsize=(10, 6))
for i in range(1, 7):
    plt.plot(avg_temperatures, grouped_data[f'B_仪器{i}_B'], color=colors[i-1], marker=markers[i-1], label=f'Instrument {i}')
plt.title('Relationship between Temperature and Average B Values of Instruments')
plt.xlabel('Temperature (°C)')
plt.ylabel('Average B Value')
plt.legend()
plt.grid(True)
plt.show()

# 绘制R值与温度的关系图
plt.figure(figsize=(10, 6))
for i in range(1, 7):
    plt.plot(avg_temperatures, grouped_data[f'R_仪器{i}_R'], color=colors[i-1], marker=markers[i-1], label=f'Instrument {i}')
plt.title('Relationship between Temperature and Average R Values of Instruments')
plt.xlabel('Temperature (°C)')
plt.ylabel('Average R Value')
plt.legend()
plt.grid(True)
plt.show()