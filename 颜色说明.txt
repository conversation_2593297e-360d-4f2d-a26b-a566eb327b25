result.xlsx 颜色和格式说明

数据区分颜色：
================

🔵 白板数据 - 浅蓝色背景 (#E6F3FF)
   位置：第2列

🟢 校准数据 - 浅绿色背景 (#E6FFE6)  
   位置：第3列

🔴 皮肤1数据 - 浅红色背景 (#FFE6E6)
   位置：第5-14列（标题为"1"）

🟠 皮肤2数据 - 浅橙色背景 (#FFF0E6)
   位置：第15-24列（标题为"2"）

🟡 皮肤3数据 - 浅黄色背景 (#FFFFE6)
   位置：第25-34列（标题为"3"）

🟣 皮肤4数据 - 浅紫色背景 (#F0E6FF)
   位置：第35-44列（标题为"4"）

🔵 皮肤5数据 - 浅蓝紫色背景 (#E6F0FF)
   位置：第45-54列（标题为"5"）

⚫ 标题和标签 - 灰色背景 (#D0D0D0)
   位置：第1行（标题）和第1列（行标签）

格式特点：
==========
- 所有数据单元格都有细边框
- 字体：Arial 10号
- 标题使用粗体
- 数据使用常规字体
- G、B值显示为整数
- R值保留2位小数

数据布局：
==========
第2行：10.txt的G值
第3行：10.txt的B值  
第4行：10.txt的R值
第5行：20.txt的G值
第6行：20.txt的B值
第7行：20.txt的R值

折线图功能：
=============
为每种皮肤（皮肤1-5）自动生成GBR折线图：
- 图表标题：显示对应的皮肤类型
- X轴：测量序号（1-10）
- Y轴：数值大小
- 6条线：
  * 10.txt G值（实线）
  * 10.txt B值（实线）
  * 10.txt R值（实线）
  * 20.txt G值（虚线）
  * 20.txt B值（虚线）
  * 20.txt R值（虚线）
- 图例：右侧显示，便于识别各条线
- 位置：数据表格下方，5个图表垂直排列

这样的颜色区分和图表可以让您：
- 快速识别不同类型的数据
- 轻松比较10.txt和20.txt的数据
- 清楚看到每个皮肤分类的测量结果
- 直观观察数据变化趋势
- 方便进行数据分析和验证
