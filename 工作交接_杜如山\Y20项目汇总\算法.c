void algo_calc(uint32_t dark, uint32_t green, uint32_t blue,uint32_t tempture)
{
//    NRF_LOG_INFO("algo_calc: dark = %4d, green = %4d, blue = %4d tempture = %4d", dark, green, blue,tempture);
	  float zzhx0,zzhk1,zzhb1;
	  uint32_t curtime = 0;
//		uint32_t curtemp = 0;
//		uint32_t lasttemp = 0;
		temprecord tmp_con_data;
    //异常判断。说明书：错误代码E01：根据医院测量的数据限定原始数据范围，再加入仿皮肤色屏的范围
	//婴儿皮肤G范围：108~491，B范围：165~783
	//仿皮肤色屏G范围：650~927，B范围：691~1022
	//综合G范围：108~927，B范围：165~1022w
	//对于DARK值的选择：当设为150时，室内和即将E01时结果值相差1左右，不能用，调整到75时，相差0.2可以接受，且肉眼对环境光看起来区别不大
	if (is_can_test_aldult)//能测量成人，连接上位机之后临时打开
	{
		if (dark > 300)
		{
			NRF_LOG_INFO("CER 1");
			algo_error_state = 1;
		}
		else if (green < 80 || green > 1000)
		{
			NRF_LOG_INFO("CER 2");
			algo_error_state = 1;
		}
		else if (blue < 130 || blue > 1200)
		{
			NRF_LOG_INFO("CER3 1");
			algo_error_state = 1;
		}
		//正常
		else
		{
			algo_error_state = 0;
		}
	}
	else//不能测量成人，产品正常使用时
	{
		if (dark > 300)
		{ 
			NRF_LOG_INFO("AER 1");
			algo_error_state = 1;
		}
		else if (green < 80 || green > 520)
		{
			NRF_LOG_INFO("AER 2");
			algo_error_state = 1;
		}
		else if (blue < 130 || blue > 820)
		{
			NRF_LOG_INFO("AER3");
			algo_error_state = 1;
		}
		//正常
		else
		{
			algo_error_state = 0;
		}
	}


	
		//以下添加温度校正
		get_temp_data(&tmp_con_data);
    	double  mb,mg,mt,mt0;
		uint32_t timediff = 0;
		//uint32_t	max_tempture = 0;
		float getcool = 0; 
		float getcoolresult =0;
		float tsensor = 0;
		mt0 = (cali_para.temph<<8)+cali_para.templ;
		curtime =  GetTimeStamp();//得到现在的时间
		timediff = curtime-tmp_con_data.last_time;//现在的时间和上一次测量的时间差

		getcool = get_tcool(tmp_con_data.last_temp); //获得自然冷却系数
		getcoolresult = getcool * (curtime-tmp_con_data.last_time)/60;//冷却系数乘时间，得到冷却值

			if(timediff> 5*60)
			{
					tmp_con_data.last_temp = tempture;
					tmp_con_data.max_tempture = tempture + 1000;
			}
			else
			{
					tmp_con_data.last_temp =  tmp_con_data.last_temp - getcoolresult;
					tmp_con_data.max_tempture = tmp_con_data.max_tempture + 1.893123*(timediff);

			}
			//本段为：判断与上一次测量的时间间隔是否大于5分钟
			//如果大于五分钟，重置last_temp和max_tempture
			//如果小于5分钟：last_temp为上一个last_temp减冷却温度。
			//如果小于5分钟：max_tempture加升温系数*间隔时间

		  tsensor = get_t_sensor(tmp_con_data.last_temp,tmp_con_data.max_tempture);//根据last_temp和max_tempture的比值得到tsenor的值
			if(tmp_con_data.last_temp<100)
			{
					tmp_con_data.last_temp = tempture;
			}
			else
			{
					tmp_con_data.last_temp = tmp_con_data.last_temp + tsensor;
			}
			if(tmp_con_data.last_temp > tmp_con_data.max_tempture)
			{
					tmp_con_data.last_temp = tmp_con_data.max_tempture;
			}

			//本段为温度补偿
			//如果last_temp<1,last_temp = tempture：温度较低时不进行补偿
			//如果last_temp > max_tempture：如果超过升温上限，把温度设置为上限温度
			//其他情况，last_temp + tsensor：加上传感器升温

			tmp_con_data.last_time = curtime;  //更新时间

		set_temp_data(&tmp_con_data);
		tempture = tmp_con_data.last_temp;
#if 1
		if((tempture<5000)&&(tempture>500))
		{				
				mt = (tempture - mt0)/100;
				if(mt<0)
				{					
					mb = blue/(1-0.006118*mt);
					mg = green/(1-0.02298*mt); 
				}
				else
				{
					mb = blue/(1-0.002088*mt);
					mg = green/(1-0.012798*mt);
				}
		}
		else
		{
			mt = 0 ;
		}
#else
		mb = blue;
		mg = green; 
#endif
//本段代码为：根据温度进行蓝光绿光的补偿
//依靠ADC温度和标准温度进行对比
//不同温度补偿系数不同





		if(((mb - dark - lb)>0)&&((mg - dark))&&((cali_para.b0 - cali_para.d0 - lb)>0)&&(cali_para.g0 - cali_para.d0 - lg>0))
		{
			float a = log10((float)(mb - dark - lb)) / log10((float)(cali_para.b0 - cali_para.d0 - lb)) + log10((float)(mb - dark) / cali_para.b0);
			float b = log10((float)(mg - dark - lg)) / log10((float)(cali_para.g0 - cali_para.d0 - lg)) + log10((float)(mg - dark) / cali_para.g0);
			float c = log10((float)(mg - dark) / (cali_para.g0 - lg)) - log10((float)(mb - dark) / (cali_para.b0 - lb));
			float d = log10((float)(mg - dark) / (cali_para.g0 - lg)) / log10((float)(mb - dark) / (cali_para.b0 - lb));
			float x = -0.9975f * a + 0.55f * b - 0.202f * c - 0.195f *d;

			bilirubin = 38.105f * x + 6.467f;
						//调整结果		
			bilirubin = bilirubin * cali_para.result_ratio / 100;
			zzhx0 = 0;
			zzhk1 = 0;
			zzhb1 = 0;
			zzhb1 = zzhx0 + zzhk1;

//分段
			if(cali_para.result_ratio !=100)
			{
				if(bilirubin>=11)
				{
							zzhx0 = ((float)11.0f/((float)cali_para.result_ratio/100.0f)-(float)6.467)/(float)38.105f;
							if(x > zzhx0)
							{
									zzhk1 = (float)7.0f/((float)0.30266-zzhx0);
									zzhb1 = (float)11.0f- zzhk1*zzhx0;
									bilirubin = zzhk1 * x + zzhb1;								
							}
				}
				else if(bilirubin<=8)
				{	
					if(cali_para.result_ratio<100)
					{

							zzhx0 = ((float)8.0f/((float)cali_para.result_ratio/100.0f)-(float)6.467)/(float)38.105f;
							if(x<zzhx0)
							{
									zzhk1 = (float)28.0f/(zzhx0+(float)0.69458);
									zzhb1 = (float)8.0f- zzhk1*zzhx0;
									bilirubin = zzhk1 * x + zzhb1;

							}
					}
				}
			}


		}
		else
		{
			algo_error_state = 1;
			bilirubin = 99;
		}


float get_tcool(uint32_t temp)
{
		float tcool=0.0;
		if(temp<1500)
		{
				tcool=-3.34375 + (temp-1000)*0.41406/100;
		}
		else if(temp<1900)
		{
				tcool=-1.6875 + (temp-1500)*0.24219/100;
		}
		else if(temp<2500)
		{
				tcool=-0.71875 + (temp-1900)*0.11979/100;
		}
		else if(temp<3000)
		{
				tcool=(temp-2500)*0.15/100;
		}
		else
		{
				tcool=0.75 + (temp-3000)*0.14583/100;
		}
		return 100.0*(float)tcool;
}

float get_t_sensor(uint32_t temp,uint32_t max_temp)
{
	float tsensor;
	float percentage = (float)temp/(float)max_temp*100;
	if (percentage < (float) 81)
	{
		tsensor = 70;
	}
	else if (percentage < (float) 90)
	{
		tsensor = 50;
	}
	else if (percentage < (float) 95)
	{
		tsensor = 30;
	}
	else
	{
		tsensor = 10;
	}
	return tsensor;
}


void get_temp_data(temprecord *ptemp_data)
{
	 memcpy(ptemp_data,&gtemp_data,sizeof(temprecord));
}

void set_temp_data(temprecord *ptemp_data)
{
	 memcpy(&gtemp_data,ptemp_data,sizeof(temprecord));
}
}