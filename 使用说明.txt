数据分类和格式转换工具使用说明

功能：
1. 数据分类：校准，白板，皮肤1，皮肤2，皮肤3，皮肤4，皮肤5
2. 过滤：过滤"Got BLE CMD49"和"Got BLE CMD19"
3. 格式转换：将"色板CH:1   D:4   G:2712   B:2725   R:2.72 mgDL   257"转换为"G:2712  B:2725  R:2.72"

使用方法：
1. 确保10.txt和20.txt文件在同一目录下
2. 运行命令：python process_10_20_files.py
3. 结果保存在processed_results目录中

输出文件：
- classified_data.xlsx：Excel格式的分类数据
  - 汇总数据工作表：包含所有数据
  - 10文件数据工作表：仅包含10.txt的数据
  - 20文件数据工作表：仅包含20.txt的数据
  - 各分类单独工作表：每个分类的详细数据（显示文件来源）
- formatted_data.txt：格式化后的文本文件
  - 按文件分组显示：分别显示10.txt和20.txt的数据
  - 按分类汇总显示：显示每条数据的文件来源

依赖库：
- pandas
- openpyxl

安装依赖：
pip install pandas openpyxl
