数据分类和格式转换工具使用说明

功能：
1. 数据分类：校准，白板，皮肤1，皮肤2，皮肤3，皮肤4，皮肤5
2. 过滤：过滤"Got BLE CMD49"和"Got BLE CMD19"
3. 格式转换：将"色板CH:1   D:4   G:2712   B:2725   R:2.72 mgDL   257"转换为"G:2712  B:2725  R:2.72"

使用方法：
1. 确保10.txt和20.txt文件在同一目录下
2. 运行命令：python process_10_20_files.py
3. 结果保存在processed_results目录中

输出文件：
- classified_data.xlsx：Excel格式的分类数据
  - 汇总数据工作表：包含所有数据
  - 10文件数据工作表：仅包含10.txt的数据
  - 20文件数据工作表：仅包含20.txt的数据
  - 各分类单独工作表：每个分类的详细数据（显示文件来源）
- formatted_data.txt：格式化后的文本文件
  - 按文件分组显示：分别显示10.txt和20.txt的数据
  - 按分类汇总显示：显示每条数据的文件来源
- result.xlsx：填写到指定模板的结果文件
  - 将每条原始数据填写到对应位置（不是平均值）
  - 按10.txt和20.txt分别填写到指定行
  - 第2-4行：10.txt的G、B、R值
  - 第5-7行：20.txt的G、B、R值
  - 列分布：
    * 第2列：白板数据
    * 第3列：校准数据
    * 第5-14列：皮肤1数据（标题为"1"）
    * 第15-24列：皮肤2数据（标题为"2"）
    * 第25-34列：皮肤3数据（标题为"3"）
    * 第35-44列：皮肤4数据（标题为"4"）
    * 第45-54列：皮肤5数据（标题为"5"）

依赖库：
- pandas
- openpyxl

安装依赖：
pip install pandas openpyxl
