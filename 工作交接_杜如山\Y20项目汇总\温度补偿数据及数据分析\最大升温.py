import pandas as pd
import matplotlib.pyplot as plt

# 读取Excel文件
file_path = '1.20最大升温测试1.xlsx'
df = pd.read_excel(file_path)

# 提取次数和各仪器的G值
times = df['次数']
instruments_G = ['仪器1_G', '仪器2_G', '仪器3_G', '仪器4_G', '仪器5_G', '仪器6_G']
instruments_B = ['仪器1_B', '仪器2_B', '仪器3_B', '仪器4_B', '仪器5_B', '仪器6_B']
instruments_R = ['仪器1_R', '仪器2_R', '仪器3_R', '仪器4_R', '仪器5_R', '仪器6_R']
# 创建一个图形和轴
plt.figure(figsize=(14, 8)) 
# 绘制每个仪器的散点图G
for instrument in instruments_G:
    plt.scatter(times, df[instrument], label=instrument)

# 设置图例
plt.legend()

# 设置标题和轴标签
plt.title('Relationship between Times and G Values')
plt.xlabel('Times')
plt.ylabel('G Values')
# 显示网格
plt.grid(True)
# 显示图形
plt.show()

plt.figure(figsize=(14, 8)) 
# 绘制每个仪器的散点图B
for instrument in instruments_B:
    plt.scatter(times, df[instrument], label=instrument)

# 设置图例
plt.legend()

# 设置标题和轴标签
plt.title('Relationship between Times and B Values')
plt.xlabel('Times')
plt.ylabel('B Values')
# 显示网格
plt.grid(True)
# 显示图形
plt.show()