import numpy as np
from scipy.interpolate import griddata
import os
import pandas as pd
import matplotlib.pyplot as plt

os.chdir("D:\E3A\亿杉算法实习\温度补偿\新探头_亿光led")
df = pd.read_excel("2.27新探头对比1,3号.xlsx")
# 提取仪器A的数据
instrument_A_data = df[['仪器1_G', '仪器1_B', '仪器A_R']]
# 提取仪器A的数据
G = df['仪器1_G'].values
B = df['仪器1_B'].values
R = df['仪器A_R'].values

# 生成网格点（根据G和B的范围插值）
xi = np.linspace(G.min(), G.max(), 100)
yi = np.linspace(B.min(), B.max(), 100)
xi, yi = np.meshgrid(xi, yi)

# 插值到网格点（使用线性插值）
zi = griddata((G, B), R, (xi, yi), method='linear')

fig = plt.figure(figsize=(10, 7))
ax = fig.add_subplot(111, projection='3d')

# 绘制散点图
ax.scatter(instrument_A_data['仪器1_G'], instrument_A_data['仪器1_B'], instrument_A_data['仪器A_R'], c='b', marker='o')

# 设置坐标轴标签
ax.set_xlabel('G (x-axis)', fontsize=12)
ax.set_ylabel('B (y-axis)', fontsize=12)
ax.set_zlabel('R (z-axis)', fontsize=12)

# 设置标题
ax.set_title('3D plot', fontsize=16)

# 显示图形
plt.show()