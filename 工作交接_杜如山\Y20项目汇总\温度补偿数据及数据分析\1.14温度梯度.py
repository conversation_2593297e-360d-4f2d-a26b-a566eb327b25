import pandas as pd
import matplotlib.pyplot as plt

# 读取Excel文件
df = pd.read_excel('1.13温度梯度.xlsx')

# 提取温度、8个仪器的G值、B值和R值数据
temperatures = df['Temperature']
g_values = df[['Instrument1_G', 'Instrument2_G', 'Instrument3_G', 'Instrument4_G', 'Instrument5_G', 'Instrument6_G', 'Instrument7_G', 'Instrument8_G']]
b_values = df[['Instrument1_B', 'Instrument2_B', 'Instrument3_B', 'Instrument4_B', 'Instrument5_B', 'Instrument6_B', 'Instrument7_B', 'Instrument8_B']]
r_values = df[['Instrument1_R', 'Instrument2_R', 'Instrument3_R', 'Instrument4_R', 'Instrument5_R', 'Instrument6_R', 'Instrument7_R', 'Instrument8_R']]

# 创建一个新的DataFrame
data = pd.DataFrame({
    'Temperature': temperatures,
    'G_Instrument1_G': g_values['Instrument1_G'],
    'G_Instrument2_G': g_values['Instrument2_G'],
    'G_Instrument3_G': g_values['Instrument3_G'],
    'G_Instrument4_G': g_values['Instrument4_G'],
    'G_Instrument5_G': g_values['Instrument5_G'],
    'G_Instrument6_G': g_values['Instrument6_G'],
    'G_Instrument7_G': g_values['Instrument7_G'],
    'G_Instrument8_G': g_values['Instrument8_G'],
    'B_Instrument1_B': b_values['Instrument1_B'],
    'B_Instrument2_B': b_values['Instrument2_B'],
    'B_Instrument3_B': b_values['Instrument3_B'],
    'B_Instrument4_B': b_values['Instrument4_B'],
    'B_Instrument5_B': b_values['Instrument5_B'],
    'B_Instrument6_B': b_values['Instrument6_B'],
    'B_Instrument7_B': b_values['Instrument7_B'],
    'B_Instrument8_B': b_values['Instrument8_B'],
    'R_Instrument1_R': r_values['Instrument1_R'],
    'R_Instrument2_R': r_values['Instrument2_R'],
    'R_Instrument3_R': r_values['Instrument3_R'],
    'R_Instrument4_R': r_values['Instrument4_R'],
    'R_Instrument5_R': r_values['Instrument5_R'],
    'R_Instrument6_R': r_values['Instrument6_R'],
    'R_Instrument7_R': r_values['Instrument7_R'],
    'R_Instrument8_R': r_values['Instrument8_R']
})

print(df['Instrument5_G'].isnull().sum())
print(df['Instrument6_G'].isnull().sum())
print(df['Instrument7_G'].isnull().sum())
print(df['Instrument4_G'].isnull().sum())
print(df['Instrument5_B'].isnull().sum())
print(df['Instrument5_G'].dtype)

# 对相同温度的G值、B值和R值进行分组并计算平均值
grouped_data = data.groupby('Temperature').mean(numeric_only=True).reset_index()

# 提取平均温度
avg_temperatures = grouped_data['Temperature']

# 定义颜色和标记
colors = ['blue', 'green', 'red', 'cyan', 'magenta', 'yellow', 'black', 'orange']
markers = ['o', 's', '^', 'v', '>', '<', 'p', 'h']

# 绘制G值与温度的关系图
plt.figure(figsize=(10, 6))
for i in range(1, 9):
    plt.plot(avg_temperatures, grouped_data[f'G_Instrument{i}_G'], color=colors[i-1], marker=markers[i-1], label=f'Instrument {i}')
plt.title('Relationship between Temperature and Average G Values of Instruments')
plt.xlabel('Temperature (°C)')
plt.ylabel('Average G Value')
plt.legend()
plt.grid(True)
plt.show()

# 绘制B值与温度的关系图
plt.figure(figsize=(10, 6))
for i in range(1, 9):
    plt.plot(avg_temperatures, grouped_data[f'B_Instrument{i}_B'], color=colors[i-1], marker=markers[i-1], label=f'Instrument {i}')
plt.title('Relationship between Temperature and Average B Values of Instruments')
plt.xlabel('Temperature (°C)')
plt.ylabel('Average B Value')
plt.legend()
plt.grid(True)
plt.show()

# 绘制R值与温度的关系图
plt.figure(figsize=(10, 6))
for i in range(1, 9):
    plt.plot(avg_temperatures, grouped_data[f'R_Instrument{i}_R'], color=colors[i-1], marker=markers[i-1], label=f'Instrument {i}')
plt.title('Relationship between Temperature and Average R Values of Instruments')
plt.xlabel('Temperature (°C)')
plt.ylabel('Average R Value')
plt.legend()
plt.grid(True)
plt.show()