#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
验证result.xlsx填写结果
"""

import openpyxl

def verify_result():
    """验证result.xlsx的填写结果"""
    try:
        # 读取result.xlsx文件
        wb = openpyxl.load_workbook('processed_results/result.xlsx')
        ws = wb['Whole data']
        
        print("result.xlsx数据填写验证")
        print("=" * 60)
        
        # 分类对应的列
        categories = {
            'B': '白板',
            'C': '校准', 
            'E': '皮肤1',
            'F': '皮肤2',
            'G': '皮肤3',
            'H': '皮肤4',
            'I': '皮肤5'
        }
        
        # 列号映射
        col_mapping = {'B': 2, 'C': 3, 'E': 5, 'F': 6, 'G': 7, 'H': 8, 'I': 9}
        
        print("10.txt文件数据 (平均值):")
        print("-" * 30)
        for col_letter, category in categories.items():
            col_num = col_mapping[col_letter]
            g_val = ws.cell(row=2, column=col_num).value
            b_val = ws.cell(row=3, column=col_num).value
            r_val = ws.cell(row=4, column=col_num).value
            
            if g_val or b_val or r_val:
                print(f"{category}:")
                print(f"  G值: {g_val if g_val else '无数据'}")
                print(f"  B值: {b_val if b_val else '无数据'}")
                print(f"  R值: {r_val if r_val else '无数据'}")
        
        print("\n20.txt文件数据 (平均值):")
        print("-" * 30)
        for col_letter, category in categories.items():
            col_num = col_mapping[col_letter]
            g_val = ws.cell(row=5, column=col_num).value
            b_val = ws.cell(row=6, column=col_num).value
            r_val = ws.cell(row=7, column=col_num).value
            
            if g_val or b_val or r_val:
                print(f"{category}:")
                print(f"  G值: {g_val if g_val else '无数据'}")
                print(f"  B值: {b_val if b_val else '无数据'}")
                print(f"  R值: {r_val if r_val else '无数据'}")
        
        print("\n数据对比分析:")
        print("-" * 30)
        for col_letter, category in categories.items():
            col_num = col_mapping[col_letter]
            
            g_10 = ws.cell(row=2, column=col_num).value
            g_20 = ws.cell(row=5, column=col_num).value
            b_10 = ws.cell(row=3, column=col_num).value
            b_20 = ws.cell(row=6, column=col_num).value
            r_10 = ws.cell(row=4, column=col_num).value
            r_20 = ws.cell(row=7, column=col_num).value
            
            if (g_10 or g_20) and (b_10 or b_20):
                print(f"{category}:")
                if g_10 and g_20:
                    print(f"  G值差异: {abs(float(g_10) - float(g_20)):.0f}")
                if b_10 and b_20:
                    print(f"  B值差异: {abs(float(b_10) - float(b_20)):.0f}")
                if r_10 and r_20:
                    print(f"  R值差异: {abs(float(r_10) - float(r_20)):.2f}")
        
        print(f"\n✅ result.xlsx文件验证完成！")
        print("数据已成功填写到指定的行列位置。")
        
    except Exception as e:
        print(f"验证时出错: {str(e)}")

if __name__ == "__main__":
    verify_result()
