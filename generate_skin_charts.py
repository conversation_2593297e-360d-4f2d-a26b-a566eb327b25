#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
皮肤数据折线图生成器
从result.xlsx读取数据，为5种皮肤生成GBR折线图
"""

import os
import matplotlib.pyplot as plt
import openpyxl
import numpy as np

class SkinChartGenerator:
    """皮肤数据图表生成器"""
    
    def __init__(self, excel_path):
        self.excel_path = excel_path
        self.skin_data = {}
        
        # 设置中文字体
        plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei']
        plt.rcParams['axes.unicode_minus'] = False
        
    def read_excel_data(self):
        """从Excel文件读取皮肤数据"""
        try:
            wb = openpyxl.load_workbook(self.excel_path)
            ws = wb['Whole data']
            
            # 皮肤分类和对应的列范围
            skin_categories = {
                '皮肤1': (5, 14),   # 第5-14列
                '皮肤2': (15, 24),  # 第15-24列
                '皮肤3': (25, 34),  # 第25-34列
                '皮肤4': (35, 44),  # 第35-44列
                '皮肤5': (45, 54)   # 第45-54列
            }
            
            for skin_name, (start_col, end_col) in skin_categories.items():
                # 读取6行数据：G-10, B-10, R-10, G-20, B-20, R-20
                skin_data = {
                    'G-10': [],
                    'B-10': [],
                    'R-10': [],
                    'G-20': [],
                    'B-20': [],
                    'R-20': []
                }
                
                # 读取每列的数据
                for col in range(start_col, end_col + 1):
                    g_10 = ws.cell(row=2, column=col).value  # G-10
                    b_10 = ws.cell(row=3, column=col).value  # B-10
                    r_10 = ws.cell(row=4, column=col).value  # R-10
                    g_20 = ws.cell(row=5, column=col).value  # G-20
                    b_20 = ws.cell(row=6, column=col).value  # B-20
                    r_20 = ws.cell(row=7, column=col).value  # R-20
                    
                    # 只添加非空数据
                    if g_10 is not None and g_10 != '':
                        skin_data['G-10'].append(float(g_10))
                    if b_10 is not None and b_10 != '':
                        skin_data['B-10'].append(float(b_10))
                    if r_10 is not None and r_10 != '':
                        skin_data['R-10'].append(float(r_10))
                    if g_20 is not None and g_20 != '':
                        skin_data['G-20'].append(float(g_20))
                    if b_20 is not None and b_20 != '':
                        skin_data['B-20'].append(float(b_20))
                    if r_20 is not None and r_20 != '':
                        skin_data['R-20'].append(float(r_20))
                
                self.skin_data[skin_name] = skin_data
                
            print(f"成功读取Excel数据: {self.excel_path}")
            for skin_name, data in self.skin_data.items():
                total_points = sum(len(values) for values in data.values())
                print(f"  {skin_name}: {total_points} 个数据点")
                
        except Exception as e:
            print(f"读取Excel文件时出错: {str(e)}")
            
    def create_single_chart(self, skin_name, skin_data, output_dir):
        """为单个皮肤创建折线图"""
        try:
            plt.figure(figsize=(12, 8))
            
            # 定义颜色和线型
            colors_styles = {
                'G-10': ('#FF0000', '-', 'o'),    # 红色实线，圆点
                'B-10': ('#0000FF', '-', 's'),    # 蓝色实线，方点
                'R-10': ('#00AA00', '-', '^'),    # 绿色实线，三角
                'G-20': ('#FF6600', '--', 'D'),   # 橙色虚线，菱形
                'B-20': ('#6600FF', '--', 'x'),   # 紫色虚线，叉号
                'R-20': ('#FF00AA', '--', '*')    # 品红虚线，星号
            }
            
            # 绘制每个数据系列
            for data_type, values in skin_data.items():
                if len(values) > 0:
                    # 创建X轴（测试次数）
                    x_values = list(range(1, len(values) + 1))
                    
                    # 获取颜色和样式
                    color, linestyle, marker = colors_styles[data_type]
                    
                    # 绘制折线
                    plt.plot(x_values, values, 
                            color=color, 
                            linestyle=linestyle, 
                            marker=marker, 
                            markersize=6,
                            linewidth=2,
                            label=data_type,
                            alpha=0.8)
            
            # 设置图表属性
            plt.title(f'{skin_name} GBR数据变化趋势', fontsize=16, fontweight='bold')
            plt.xlabel('测试次数', fontsize=12)
            plt.ylabel('数值大小', fontsize=12)
            plt.grid(True, alpha=0.3)
            plt.legend(loc='best', fontsize=10)
            
            # 设置X轴刻度
            max_tests = max(len(values) for values in skin_data.values() if len(values) > 0)
            if max_tests > 0:
                plt.xticks(range(1, max_tests + 1))
            
            # 调整布局
            plt.tight_layout()
            
            # 保存图表
            output_path = os.path.join(output_dir, f'{skin_name}_GBR折线图.png')
            plt.savefig(output_path, dpi=300, bbox_inches='tight')
            plt.close()
            
            print(f"已生成 {skin_name} 折线图: {output_path}")
            
        except Exception as e:
            print(f"生成 {skin_name} 图表时出错: {str(e)}")
            
    def generate_all_charts(self, output_dir="charts_output"):
        """生成所有皮肤的折线图"""
        # 创建输出目录
        if not os.path.exists(output_dir):
            os.makedirs(output_dir)
            
        print(f"\n开始生成折线图...")
        print("=" * 50)
        
        # 为每种皮肤生成图表
        for skin_name, skin_data in self.skin_data.items():
            self.create_single_chart(skin_name, skin_data, output_dir)
            
        print(f"\n✅ 所有图表生成完成！")
        print(f"输出目录: {output_dir}")
        print("生成的文件:")
        for skin_name in self.skin_data.keys():
            print(f"  - {skin_name}_GBR折线图.png")
            
    def print_data_summary(self):
        """打印数据摘要"""
        print("\n数据摘要:")
        print("=" * 50)
        
        for skin_name, skin_data in self.skin_data.items():
            print(f"\n{skin_name}:")
            for data_type, values in skin_data.items():
                if len(values) > 0:
                    avg_val = sum(values) / len(values)
                    min_val = min(values)
                    max_val = max(values)
                    print(f"  {data_type}: {len(values)}个数据点, 平均值={avg_val:.2f}, 范围=[{min_val:.2f}, {max_val:.2f}]")
                else:
                    print(f"  {data_type}: 无数据")

def main():
    """主函数"""
    # Excel文件路径
    excel_path = "processed_results/result.xlsx"
    
    # 检查文件是否存在
    if not os.path.exists(excel_path):
        print(f"错误: 找不到文件 {excel_path}")
        print("请确保已运行 process_10_20_files.py 生成 result.xlsx 文件")
        return
    
    print("皮肤数据折线图生成器")
    print("=" * 50)
    print(f"读取文件: {excel_path}")
    
    # 创建图表生成器
    generator = SkinChartGenerator(excel_path)
    
    # 读取数据
    generator.read_excel_data()
    
    # 打印数据摘要
    generator.print_data_summary()
    
    # 生成图表
    generator.generate_all_charts()

if __name__ == "__main__":
    main()
