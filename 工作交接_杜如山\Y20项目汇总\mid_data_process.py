# 打开原始文件并读取内容
import os

# 打印当前工作目录
print("当前工作目录:", os.getcwd())

# 如果需要，更改工作目录
os.chdir("D:\E3A\亿杉算法实习\温度补偿")
input_file = "mid_data.txt"  # 原始文件名or
output_file = "Filtered_data.txt"  # 输出文件名

# 打开原始文件进行读取
with open(input_file, "r", encoding="utf-8") as file:
    lines = file.readlines()

# 筛选出包含“色板CH”的行
filtered_lines = [line for line in lines if "仪器" in line or "色板" in line or"D:" in line ]

# 将筛选后的内容写入新文件
with open(output_file, "w", encoding="utf-8") as file:
    file.writelines(filtered_lines)

