import pandas as pd
import matplotlib.pyplot as plt
import numpy as np

# 读取Excel文件
file_path = '1.10自然降温重置表格.xlsx'
df = pd.read_excel(file_path)

# 定义通过G值预测温度的函数
# 定义温度区间的线性回归系数和截距
temperature_ranges = [(10, 15), (15, 20), (20, np.inf)]
coefficients = [-0.6486, -0.3412, -0.1452]
intercepts = [340.9907, 187.0115, 91.1400]
def predict_temperature(g_value):
    for i, temp_range in enumerate(temperature_ranges):
        predicted_temp = coefficients[i] * g_value + intercepts[i]
        if temp_range[0] <= predicted_temp <= temp_range[1]:
            return predicted_temp
    return None

# 计算每个时间点的平均G值
df['Average_G'] = df[['仪器1_G', '仪器2_G', '仪器3_G', '仪器4_G', '仪器5_G', '仪器6_G']].mean(axis=1)

# 使用平均G值预测温度
df['Predicted_Temperature'] = df['Average_G'].apply(predict_temperature)

# 绘制时间-温度曲线
plt.figure(figsize=(10, 6))
plt.plot(df['时间'], df['Predicted_Temperature'], marker='o', linestyle='-', color='b')
plt.title('Predicted Temperature vs Time')
plt.xlabel('Time')
plt.ylabel('Predicted Temperature (°C)')
plt.grid(True)
plt.show()