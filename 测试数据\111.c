#include "power.h"

#include "nrf_drv_power.h"


// 电源初始化
void power_init(void)
{
    ret_code_t err_code;
    // 初始化电源管理模块
    err_code = nrf_pwr_mgmt_init();
    APP_ERROR_CHECK(err_code);
	
#if NRF_MODULE_ENABLED(POWER)
    // 如果启用了POWER模块，初始化电源驱动
	ret_code_t ret = nrf_drv_power_init(NULL);
	APP_ERROR_CHECK(ret);
#endif
}

// 睡眠
void power_wait(void)
{
	// 处理定时器的调度
	app_sched_execute(); // 必须加这句话，不然会卡死
	
	// 进入睡眠模式
	if (!NRF_LOG_PROCESS())
	{
		nrf_pwr_mgmt_run();
	}
}

// 休眠
void power_sleep(void)
{
    ret_code_t err_code;

	// 在进入休眠前，先准备好唤醒条件
	
	// 记录日志：进入休眠
	NRF_LOG_INFO("power_sleep\n");
	
    // 进入系统关机模式（这个函数不会返回；唤醒会导致重启）
    // 如果系统在Debug（RTT打开）模式下，调用sd_power_system_off()将会返回NRF_ERROR_SOC_POWER_OFF_SHOULD_NOT_RETURN
    err_code = sd_power_system_off();
	if (err_code == NRF_ERROR_SOC_POWER_OFF_SHOULD_NOT_RETURN)
		APP_ERROR_CHECK(err_code);
}

// 复位
void power_reset(void)
{
	// 如果日志处理完成
	if (!NRF_LOG_PROCESS())
	{
		// 触发系统复位
		NVIC_SystemReset();
	}
}
