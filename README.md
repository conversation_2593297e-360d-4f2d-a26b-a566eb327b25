# 数据分类和格式转换工具

## 功能说明

这个Python脚本可以自动处理10.txt和20.txt文件，实现以下功能：

1. **数据分类**：自动识别并分类数据为7个类别
   - 校准
   - 白板
   - 皮肤1
   - 皮肤2
   - 皮肤3
   - 皮肤4
   - 皮肤5

2. **数据过滤**：自动过滤掉包含以下命令的行
   - `Got BLE CMD49`
   - `Got BLE CMD19`

3. **格式转换**：将原始数据格式转换为标准格式
   - 原始格式：`色板CH:1   D:4   G:2712   B:2725   R:2.72 mgDL   257`
   - 转换后：`G:2712  B:2725  R:2.72`

## 使用方法

### 运行脚本
```bash
python process_10_20_files.py
```

### 输出文件

脚本会在 `processed_results` 目录中生成以下文件：

1. **classified_data.xlsx** - Excel格式的分类数据（参照example.xlsx格式）
   - **Whole data工作表**：包含所有分类的G、B、R值汇总
   - **Green工作表**：专门显示各分类的G值数据
   - **Blue工作表**：专门显示各分类的B值数据
   - **R工作表**：专门显示各分类的R值数据
   - **原始数据汇总工作表**：包含所有原始处理数据

2. **formatted_data.txt** - 格式化后的文本文件
   - 按分类整理的所有转换后数据

3. **数据可视化图表**：
   - `category_distribution.png` - 各分类数据数量分布图
   - `g_b_scatter.png` - G值与B值关系散点图
   - `r_value_trend.png` - R值变化趋势图

## 处理结果示例

### 数据统计
- 总共处理：104 条数据
- 校准：1 条数据
- 白板：2 条数据
- 皮肤1：20 条数据
- 皮肤2：20 条数据
- 皮肤3：21 条数据
- 皮肤4：20 条数据
- 皮肤5：20 条数据

### 格式转换示例
```
原始数据：色板CH:1   D:4   G:2712   B:2725   R:2.72 mgDL   257
转换后：  G:2712  B:2725  R:2.72

原始数据：G:2893，B:2643   GPWM=148    BPWM=53
转换后：  G:2893  B:2643  R:N/A
```

## 技术特点

- **自动分类识别**：根据文件中的分类标签自动识别数据类别
- **智能数据提取**：使用正则表达式精确提取G、B、R数值
- **多格式支持**：支持多种不同的原始数据格式
- **数据可视化**：自动生成图表帮助分析数据分布和趋势
- **Excel报表**：参照example.xlsx格式生成标准化Excel报表，便于进一步分析
- **模板兼容**：完全按照example.xlsx的工作表结构和数据布局生成结果

## 依赖库

- pandas
- matplotlib
- seaborn
- openpyxl

## 文件结构

```
├── process_10_20_files.py    # 主处理脚本
├── 10.txt                    # 输入数据文件1
├── 20.txt                    # 输入数据文件2
├── processed_results/        # 输出结果目录
│   ├── classified_data.xlsx  # Excel分类数据
│   ├── formatted_data.txt    # 格式化文本数据
│   ├── category_distribution.png  # 分类分布图
│   ├── g_b_scatter.png       # G-B散点图
│   └── r_value_trend.png     # R值趋势图
└── README.md                 # 说明文档
```

## 注意事项

1. 确保10.txt和20.txt文件在脚本同一目录下
2. 脚本会自动创建processed_results目录
3. 如果缺少依赖库，请使用pip安装：
   ```bash
   pip install pandas matplotlib seaborn openpyxl
   ```
