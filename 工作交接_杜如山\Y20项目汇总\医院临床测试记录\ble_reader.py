import asyncio
import pandas as pd
import numpy as np
from bleak import BleakClient, BleakScanner
from datetime import datetime
import re
import openpyxl

# UUIDs for the UART service characteristics
WRITE_CHARACTERISTIC_UUID = "6e400002-b5a3-f393-e0a9-e50e24dcca9e"
NOTIFY_CHARACTERISTIC_UUID = "6e400003-b5a3-f393-e0a9-e50e24dcca9e"

# Commands (full BLE documentation of YSJ-20 can be found in 经皮黄疸仪（YSJ-20）APP蓝牙部分开发需求 2023-1-29.docx)
READ_HISTORY_COMMAND = bytearray([0x37, 0x01, 0x00])

async def scan_ble_devices():
    print("Scanning for BLE devices...")
    devices = await BleakScanner.discover()
    for device in devices:
        if device.name !=None and device.name[:6] == "YSJ-20":
            print(f"Device: {device.name}, Address: {device.address}")
    return devices

# Function to parse byte array
def parse_bytearray(data):
    try:
        command_byte = data[0]
        decoded_str = data[1:].decode("utf-8")
        parts = decoded_str.split("\t")

        f=filter(str.isnumeric, parts[0].strip("~"))
        dark="".join(f)
        blue = parts[1]
        green = parts[2]
        measurement_result = parts[3]
        timestamp_raw = parts[4].strip("@")

        timestamp = int(timestamp_raw)
        readable_timestamp = datetime.fromtimestamp(timestamp).strftime('%Y-%m-%d %H:%M:%S')

        parsed_data = {
            "command_byte": command_byte,
            "dark": dark,
            "green": blue,
            "blue": green,
            "ysj_readings": float(measurement_result),
            "timestamp": readable_timestamp
        }

        return parsed_data

    except Exception as e:
        print(f"Error parsing byte array: {e}")
        return None

# Function to parse the second data read for misc values
def parse_misc_values(data):
    # Assuming the data structure looks like 'zero: 0, 1058, 3030, air: 0, 24, 47,, 101'
    data_str = data[1:].decode("utf-8")
    misc_values = re.findall(r'\d+', data_str)  # Extract all numeric values from the string

    if len(misc_values) >= 6:
        d0 = int(misc_values[0])
        g0 = int(misc_values[1])
        b0 = int(misc_values[2])
        d1 = int(misc_values[3])
        g1 = int(misc_values[4])
        b1 = int(misc_values[5])
        return d0, g0, b0, d1, g1, b1
    else:
        print("Error parsing misc values from second read.")
        return None, None, None, None, None, None

async def notification_handler(client, sender, data, history_list, data_buffer, misc_values, last_data_time):
    print(f"Raw notification data (chunk): {data}")
    data_buffer.extend(data)

    # Update the last data received time
    last_data_time[0] = asyncio.get_event_loop().time()

    # If this is the second data read, parse misc values
    if b'zero:' in data:
        d0, g0, b0, d1, g1, b1 = parse_misc_values(data)
        misc_values.extend([d0, g0, b0, d1, g1, b1])

    if data_buffer.endswith(b'@@@'):
        parsed_data = parse_bytearray(data_buffer)
        if parsed_data:
            print(f"Parsed history data (JSON): {parsed_data}")
            history_list.append(parsed_data)

        data_buffer.clear()

async def monitor_timeout(client, last_data_time, timeout_duration):
    while True:
        await asyncio.sleep(1)
        current_time = asyncio.get_event_loop().time()
        if current_time - last_data_time[0] > timeout_duration:
            print("No new data received. Stopping notifications.")
            await client.stop_notify(NOTIFY_CHARACTERISTIC_UUID)
            break

async def read_history_data(address, date_filter, hosp_device):
    async with BleakClient(address) as client:
        print(f"Connected to {address}")
        history_list = []
        data_buffer = bytearray()
        misc_values = []  # To hold the misc values like d0, g0, b0, etc.

        # List to hold the last data received time
        last_data_time = [asyncio.get_event_loop().time()]  # Mutable to be updated in handler

        timeout_duration = 5  # Set the timeout duration in seconds

        try:
            await client.start_notify(NOTIFY_CHARACTERISTIC_UUID,
                                       lambda s, d: asyncio.create_task(notification_handler(client, s, d, history_list, data_buffer, misc_values, last_data_time)))
            print(f"Listening for history data on {NOTIFY_CHARACTERISTIC_UUID}")

            print(f"Sending read history command: {READ_HISTORY_COMMAND}")
            await client.write_gatt_char(WRITE_CHARACTERISTIC_UUID, READ_HISTORY_COMMAND)
            print("Command sent successfully")

            # Start the timeout monitoring in a separate task
            await monitor_timeout(client, last_data_time, timeout_duration)

            # Create DataFrame from history list
            df = pd.DataFrame(history_list)

            # Convert timestamp to datetime
            df['dates'] = pd.to_datetime(df['timestamp'])

            # Filter by the date provided by the user
            # df['date'] = df['dates'].dt.strftime('%d%b')  # Format the date as '23Jul', '31Aug', etc.
            # df = df[df['date'] == date_filter]

            # Sorting by timestamps
            df.sort_values(by=['dates'], inplace=True)

            # Insert calibration values if they were successfully parsed
            if len(misc_values) == 6:
                df["d0"], df["g0"], df["b0"], df["d1"], df["g1"], df["b1"] = misc_values
            else:
                print("Misc values could not be extracted, default values will not be added.")

            # Adding empty columns for manual keying in of ground truth/labels
            if hosp_device == "dw":
                df["dw_readings"] = np.nan
            else:
                df["mnd_readings"] = np.nan
            df["forehead_chest"] = np.nan
            df["blood_raw"] = np.nan
            df["blood_converted"] = np.nan
            
            df = df[["dates", "dark", "green", "blue", "d0", "g0", "b0", "d1",  "g1", "b1", "ysj_readings", "dw_readings", "forehead_chest", "blood_raw", "blood_converted"]]

            # Save the DataFrame to an Excel file for analysis
            save_path = f"D:\E3A\亿杉算法实习\温度补偿\医院临床测试记录\{date_filter}.xlsx"
            df.to_excel(save_path, index=False)
            print(f"Data saved to {date_filter}.xlsx")

        except Exception as e:
            print(f"Error during communication: {e}")

async def main():
    await scan_ble_devices()
    address = input("Enter the address of the BLE device to connect: ")
    date_filter = input("Enter the date of the data collection (e.g., '23Jul'): ")
    hosp_device = input("Enter the hospital device type ('dw' or 'mnd'): ")

    await read_history_data(address, date_filter, hosp_device)

if __name__ == "__main__":
    asyncio.run(main())
