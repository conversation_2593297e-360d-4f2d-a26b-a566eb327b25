#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试数据分类器的功能
"""

from process_10_20_files import DataClassifier
import os

def test_data_extraction():
    """测试数据提取功能"""
    classifier = DataClassifier()
    
    # 测试用例
    test_cases = [
        "色板CH:1   D:4   G:2712   B:2725   R:2.72 mgDL   257",
        "G:2893，B:2643   GPWM=148    BPWM=53",
        "Got BLE CMD49",
        "Got BLE CMD19",
        "校准：",
        "皮肤1："
    ]
    
    print("测试数据提取功能:")
    print("-" * 40)
    
    for i, test_line in enumerate(test_cases, 1):
        print(f"测试 {i}: {test_line}")
        
        # 测试过滤功能
        should_filter = classifier.should_filter_line(test_line)
        print(f"  是否过滤: {should_filter}")
        
        # 测试数据提取
        if not should_filter:
            gbr_result = classifier.extract_gbr_values(test_line)
            print(f"  提取结果: {gbr_result}")
        
        print()

def test_file_processing():
    """测试文件处理功能"""
    print("测试文件处理功能:")
    print("-" * 40)
    
    classifier = DataClassifier()
    
    # 检查文件是否存在
    files = ['10.txt', '20.txt']
    for file in files:
        if os.path.exists(file):
            print(f"✓ 文件 {file} 存在")
        else:
            print(f"✗ 文件 {file} 不存在")
    
    print()

def show_results():
    """显示处理结果"""
    print("处理结果展示:")
    print("-" * 40)
    
    # 检查输出文件
    output_files = [
        'processed_results/classified_data.xlsx',
        'processed_results/formatted_data.txt',
        'processed_results/category_distribution.png',
        'processed_results/g_b_scatter.png',
        'processed_results/r_value_trend.png'
    ]
    
    for file in output_files:
        if os.path.exists(file):
            size = os.path.getsize(file)
            print(f"✓ {file} ({size} bytes)")
        else:
            print(f"✗ {file} 不存在")
    
    print()

def main():
    """主测试函数"""
    print("="*60)
    print("数据分类器功能测试")
    print("="*60)
    
    test_data_extraction()
    test_file_processing()
    show_results()
    
    print("测试完成！")

if __name__ == "__main__":
    main()
