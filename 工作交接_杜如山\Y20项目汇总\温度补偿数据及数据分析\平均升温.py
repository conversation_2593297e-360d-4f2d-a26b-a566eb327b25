import pandas as pd
import matplotlib.pyplot as plt
import numpy as np
from sklearn.linear_model import LinearRegression

# 读取Excel文件
file_path = '1.16最大升温-20度.xlsx'
df = pd.read_excel(file_path)

# 提取次数
times = df['次数']

# 定义温度区间的线性回归系数和截距
# temperature_ranges = [(10, 15), (15, 20), (20, np.inf)]
# coefficients = [-0.3449, -0.1508, -0.1469]
# intercepts = [185.5723, 88.1286, 86.9757]
# temperature_ranges = [(10, np.inf)]
# coefficients = [-0.1469]
# intercepts = [86.9757]
temperature_ranges = [(10, 15), (15, 20), (20, np.inf)]
coefficients = [-0.6486, -0.3412, -0.1452]
intercepts = [340.9907, 187.0115, 91.1400]

# 定义通过G值预测温度的函数
def predict_temperature(g_value):
    for i, temp_range in enumerate(temperature_ranges):
        predicted_temp = coefficients[i] * g_value + intercepts[i]
        if temp_range[0] <= predicted_temp <= temp_range[1]:
            return predicted_temp
    return None

# 初始化一个列表来存储每个次数的平均温度
average_temperatures = []

# 计算每个次数的平均温度
for time in times.unique():
    time_index = times[times == time].index  # 找到当前次数的所有索引
    predicted_temps = []
    
    for instrument in ['仪器1', '仪器2', '仪器3', '仪器4', '仪器5', '仪器6']:
        g_values = df[f'{instrument}_G'].iloc[time_index]
        for g in g_values:
            predicted_temp = predict_temperature(g)
            if predicted_temp is not None:
                predicted_temps.append(predicted_temp)
    
    if predicted_temps:
        average_temperature = np.mean(predicted_temps)
        average_temperatures.append((time, average_temperature))

# 将结果转换为DataFrame
average_temperatures_df = pd.DataFrame(average_temperatures, columns=['次数', '平均温度'])

# 绘制次数与平均温度的关系图
plt.figure(figsize=(14, 8))
plt.scatter(average_temperatures_df['次数'], average_temperatures_df['平均温度'], color='blue', label='Average Predicted Temperature')


# 添加图例
plt.legend()

# 添加标题和轴标签
plt.title('Relationship between Times and Average Temperatures')
plt.xlabel('Times')
plt.ylabel('Average Predicted Temperature (°C)')
plt.grid(True)
plt.show()

# 添加图例
plt.legend()

