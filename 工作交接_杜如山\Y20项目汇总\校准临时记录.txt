#include "sm_calibrate.h"
#include "analog.h"
#include "algo.h"
#include "utils.h"
#include "storage.h"
#include "frame.h"
#include "power.h"
#include "motor.h"
#include "protocol.h"
#include "pwm.h"
#include "saadc.h"
#include "nrf_drv_saadc.h"


//#define LED_GREEN_DUTY      (155)  //50    //70 OK
//#define LED_BLUE_DUTY       (165)  //18    //80 OK
#define LED_GREEN_DUTY     182 //(160)  //50    //70 OK 新算法有改动校准修改
#define LED_BLUE_DUTY       60 //(67)  //18    //80 OK这个是校准开始时使用的初始值
#define LED_DUTY_ADJ_MAXCNT   30   //divid --- 15
#define LED_GREEN_ADCVAL      3000 //1000 新算法有改动
#define LED_GREEN_ADCTOL      15    //divid --15
#define LED_BLUE_ADCVAL       3000   //divid --3000
#define LED_BLUE_ADCTOL       15

static uint32_t sm_tick_ms;
static uint32_t stage = 0;  //校准阶段，0表示未开始，1~2表示阶段，3表示已完成
static uint32_t average_count = 0;  //需要采集多次做平均
static uint32_t raw_data[4][CALI_AVERAGE_TIMES];
static cali_para_t cali_raw_para;
static uint32_t cali_mode;//校准模式，2表示普通模式，用白色色屏，3表示特殊模式，使用黄色色屏
static bool autoagj = true;



void sm_calibrate_init(uint32_t para)
{
		battery_level_t mbat;
    sm_tick_ms = 0;
    
    memset(&cali_raw_para, 0, sizeof(cali_raw_para));
    
    //仅当电池电量充足时才能进行校准
	  mbat = get_battery_level();    
    if ( mbat >= BATTERY_LEVEL_LOW)
    {
        stage = 1;
        average_count = 0;
				cali_mode = para;
//				if (cali_mode == 2)
//				{
//						NRF_LOG_INFO("normal cali");
//				}
//				else if (cali_mode == 3)
//				{
//						NRF_LOG_INFO("special cali");
//				}
        page_calibrate_1();
				
        motor_start_once(10);
		//		sm_set_auto_power_off_time(AUTO_POWER_OFF_SECOND ); 
			
    }
    else
    {
        stage = 0;
        page_battery_empty();
    }
}


void sm_calibrate_tick(void)
{
    if (stage == 3)
    {
        sm_tick_ms++;
        if (sm_tick_ms == 2000)
        {
            sm_jump(SM_READY, 0);
        }
    }
}


void sm_calibrate_event(ui_evt_t ui_evt)
{
    //用户按键
    if ((ui_evt.ui_evt_type == UI_EVT_TYPE_KEY)
        && (ui_evt.evt.ui_evt_key.key_instance == KEY_INSTANCE_KEY_USER)
        && (ui_evt.evt.ui_evt_key.key_evt_type == KEY_EVENT_TYPE_RELEASED))
    {
        sm_jump(SM_READY, 0);
    }
    //按压开关
    else if ((ui_evt.ui_evt_type == UI_EVT_TYPE_KEY)
        && (ui_evt.evt.ui_evt_key.key_instance == KEY_INSTANCE_KEY_PRESS)
        && (ui_evt.evt.ui_evt_key.key_evt_type == KEY_EVENT_TYPE_PRESSED))
    {
				
        if (stage == 1 || stage == 2)
        {
									
            //清屏
            page_clear();
						autoagj = true;
						if((average_count==0)&&(stage == 1))   //G 1000 B  3000 PWM 自动调节
						{
								uint32_t  i =0,y = 0;
								pwm_led_set_duty(0);
		
								nrf_delay_ms(5);
							  uint32_t gstartduty = LED_GREEN_DUTY;
								uint32_t bstartduty =	LED_BLUE_DUTY;
								cali_raw_para.g_pwm = LED_GREEN_DUTY;
								cali_raw_para.b_pwm = LED_BLUE_DUTY;
								nrf_gpio_pin_clear(LED_B_CUT_PIN);
								nrf_gpio_pin_set(LED_G_CUT_PIN);				//先调节G
								pwm_led_set_duty(gstartduty);
			
								nrf_delay_ms(5);
								y =  saadc_convert(SAADC_CHANNEL_AMP2, 5);
//								NRF_LOG_INFO("ADCPREG = %d",y);
//								NRF_LOG_PROCESS();
								if(y >= LED_GREEN_ADCVAL + LED_GREEN_ADCTOL)
								{
										i = 0;
										while(true)
										{
													pwm_led_set_duty(--gstartduty);
//													NRF_LOG_INFO("ADCG- = %d,gduty = %d",y,gstartduty);
//													NRF_LOG_PROCESS();


													nrf_delay_ms(5);

													y =  saadc_convert(SAADC_CHANNEL_AMP2, 5);
											    if(y<200)
													{
														  autoagj = false;
															break; 
													}
											    if(y <= LED_GREEN_ADCVAL + LED_GREEN_ADCTOL )
													{													
														 break;
													}
													if(i++ > LED_DUTY_ADJ_MAXCNT )
													{									
															autoagj = false;
															break; 
													}
													
										}
										if(!autoagj)
										{
													
												  goto adjerror;
										}
										else
										{
											 cali_raw_para.g_pwm = gstartduty + 1;
										}
									
								}
								else if(y < LED_GREEN_ADCVAL - LED_GREEN_ADCTOL )
								{
										i = 0;
										while(true)
										{
													pwm_led_set_duty(++gstartduty);
//													NRF_LOG_INFO("ADCG+ = %d,gduty = %d",y,gstartduty);
//													NRF_LOG_PROCESS();
									
													nrf_delay_ms(5);

													y =  saadc_convert(SAADC_CHANNEL_AMP2, 5);
													if(y<200)
													{
														  autoagj = false;
															break; 
													}
											    if(y >= LED_GREEN_ADCVAL - LED_GREEN_ADCTOL)
													{
	//														cali_raw_para.g_pwm = gstartduty;
															break;
													}
													if(i++ > LED_DUTY_ADJ_MAXCNT )
													{													
															autoagj = false;
															break;
													}
													
										}
										if(!autoagj)
										{
													
												  goto adjerror;
										}
										else
										{
											 cali_raw_para.g_pwm = gstartduty + 1 ;
										}
								}
								pwm_led_set_duty(0);
								nrf_delay_ms(5);
								nrf_gpio_pin_clear(LED_G_CUT_PIN);
								nrf_gpio_pin_set(LED_B_CUT_PIN);	
								pwm_led_set_duty(bstartduty);
								nrf_delay_ms(5);

								y =  saadc_convert(SAADC_CHANNEL_AMP1, 5);
//							NRF_LOG_INFO("ADCPREB = %d",y);
//							NRF_LOG_PROCESS();
								if(y > LED_BLUE_ADCVAL + LED_BLUE_ADCTOL )
								{
										i = 0;
										while(true)
										{
													pwm_led_set_duty(--bstartduty);
//													NRF_LOG_INFO("ADCB- = %d,bduty = %d",y,bstartduty);
//													NRF_LOG_PROCESS();
//													nrfx_saadc_calibrate_offset();   //ADC温度自动校准
//												  while (nrf_drv_saadc_is_busy());   //等待校准完成
													nrf_delay_ms(5);

													y =  saadc_convert(SAADC_CHANNEL_AMP1, 5);
													if(y<200)
													{
														  autoagj = false;
															break; 
													}
											    if(y <= LED_BLUE_ADCVAL + LED_BLUE_ADCTOL )
													{												
															break;
													}
													if(i++ > LED_DUTY_ADJ_MAXCNT )
													{				
														
															autoagj = false;
															break; 
													}
													
													
										}
										if(!autoagj)
										{
//													NRF_LOG_INFO("ERROR ADCB- = %d",y);
//													NRF_LOG_PROCESS();
												  goto adjerror;
										}
										else
										{
												 cali_raw_para.b_pwm = bstartduty + 1;
										}
									
								}
								else if(y < LED_BLUE_ADCVAL - LED_BLUE_ADCTOL)
								{
										i = 0;
										while(true)
										{
													pwm_led_set_duty(++bstartduty);
//													nrfx_saadc_calibrate_offset();   //ADC温度自动校准
//												  while (nrf_drv_saadc_is_busy());   //等待校准完成
													nrf_delay_ms(5);

													y =  saadc_convert(SAADC_CHANNEL_AMP1, 5);
//													NRF_LOG_INFO("ADCB+ = %d,bduty = %d",y,bstartduty);
//													NRF_LOG_PROCESS();
													if(y<200)
													{
														  autoagj = false;
															break; 
													}
											    if(y >= LED_BLUE_ADCVAL - LED_BLUE_ADCTOL)
													{
															cali_raw_para.b_pwm = bstartduty;
															break;
													}
													if(i++ > LED_DUTY_ADJ_MAXCNT )
													{
															autoagj = false;
															break; 
													}
													
										}
										if(!autoagj)
										{
//													NRF_LOG_INFO("ERROR ADCB+ = %d",y);
//													NRF_LOG_PROCESS();
												  goto adjerror;
										}
										else
										{
												 cali_raw_para.b_pwm = bstartduty -1;
										}
								}
//								NRF_LOG_INFO("pwmg = %d,pwmb = %d",gstartduty,bstartduty);
//								NRF_LOG_PROCESS();
								storage_update_duty(gstartduty,bstartduty);
							
						}			
											
						adjerror:
						pwm_led_set_duty(0);
						nrf_delay_ms(10);
						nrf_gpio_pin_clear(LED_G_CUT_PIN);
						nrf_gpio_pin_clear(LED_B_CUT_PIN);
						
						if(!autoagj)
						{
							NRF_LOG_INFO("calibrate wrong");
							sm_jump(SM_ERROR, 12);
						}
						else
						{
							analog_measure(0);
						}								
        }
		}
    //测量完成
    else if (ui_evt.ui_evt_type == UI_EVT_TYPE_MEASURE_DONE)
    {
        if (stage == 1)
        {
            raw_data[0][average_count] = ui_evt.evt.ui_evt_measure_done.dark;
            raw_data[1][average_count] = ui_evt.evt.ui_evt_measure_done.green;
            raw_data[2][average_count] = ui_evt.evt.ui_evt_measure_done.blue;
					  raw_data[3][average_count] = ui_evt.evt.ui_evt_measure_done.tempture;
//						NRF_LOG_INFO("dd=%d,db=%d,dg =%d",ui_evt.evt.ui_evt_measure_done.dark,ui_evt.evt.ui_evt_measure_done.green,ui_evt.evt.ui_evt_measure_done.blue);
//						NRF_LOG_PROCESS();
            average_count++;
            if (average_count == CALI_AVERAGE_TIMES)
            {
//                cali_raw_para.d0 = median_filter(raw_data[0], CALI_AVERAGE_TIMES, 3);
//                cali_raw_para.g0 = median_filter(raw_data[1], CALI_AVERAGE_TIMES, 3);
//                cali_raw_para.b0 = median_filter(raw_data[2], CALI_AVERAGE_TIMES, 3);
								int dsum =0,gsum = 0,bsum =0,tsum =0 ;
								for(int i=0;i<5;i++)
								{
									 dsum += raw_data[0][i];
									 gsum += raw_data[1][i];
									 bsum += raw_data[2][i];
									 tsum += raw_data[3][i];
								}
								cali_raw_para.d0 = dsum/5;
								cali_raw_para.g0 = gsum/5;
						   		cali_raw_para.b0 = bsum/5;
								tsum = tsum/5;
								cali_raw_para.temph = (tsum >>8)&0xff;
								cali_raw_para.templ = tsum & 0xff;
                NRF_LOG_INFO("cali zero: %d, %d, %d %d %d", cali_raw_para.d0, cali_raw_para.g0, cali_raw_para.b0,cali_raw_para.temph,cali_raw_para.templ);
                stage = 2;
                page_calibrate_2();
                motor_start_once(10);
                average_count = 0;
            }
            else
            {
                page_cali_cnt(average_count, CALI_AVERAGE_TIMES);
            }
        }
        else if (stage == 2)
        {
            raw_data[0][average_count] = ui_evt.evt.ui_evt_measure_done.dark ;
            raw_data[1][average_count] = ui_evt.evt.ui_evt_measure_done.green ;
            raw_data[2][average_count] = ui_evt.evt.ui_evt_measure_done.blue ;
            average_count++;
            if (average_count == CALI_AVERAGE_TIMES)
            {
//                cali_raw_para.d1 = median_filter(raw_data[0], CALI_AVERAGE_TIMES, 3);
//                cali_raw_para.g1 = median_filter(raw_data[1], CALI_AVERAGE_TIMES, 3);
//                cali_raw_para.b1 = median_filter(raw_data[2], CALI_AVERAGE_TIMES, 3);
								int dsum =0,gsum = 0,bsum =0,tsum = 0 ;
								for(int i=0;i<5;i++)
								{
									 dsum += raw_data[0][i];
									 gsum += raw_data[1][i];
									 bsum += raw_data[2][i];
									 tsum += raw_data[3][i];
								}
								
								cali_raw_para.d1 = dsum/5;
								cali_raw_para.g1 = gsum/5;
								cali_raw_para.b1 = bsum/5;
								tsum = tsum/5;
//								cali_raw_para.temph = (tsum >>8)&0xff;
//								cali_raw_para.templ = tsum & 0xff;
                NRF_LOG_INFO("cali air: %d, %d, %d, %d, %d ", cali_raw_para.d1, cali_raw_para.g1, cali_raw_para.b1,cali_raw_para.temph,cali_raw_para.templ);
				
								if (cali_mode == 3)//特殊校准时做数据变换
								{
									cali_raw_para.g0 = (cali_raw_para.g0/* - lg*/) / 0.85f;
									cali_raw_para.b0 = (cali_raw_para.b0/* - lb*/) / 0.285f;
									NRF_LOG_INFO("special change cali zero: %d, %d, %d", cali_raw_para.d0, cali_raw_para.g0, cali_raw_para.b0);
								}
								
								//计算漏光
								uint32_t lg = minite_zero(cali_raw_para.g1, cali_raw_para.d1);
								uint32_t lb = minite_zero(cali_raw_para.b1, cali_raw_para.d1);
								
								char str[100];
								snprintf(str, sizeof(str), "zero: %d, %d, %d, air: %d, %d, %d T: %d,%d", cali_raw_para.d0, cali_raw_para.g0, cali_raw_para.b0, cali_raw_para.d1, cali_raw_para.g1, cali_raw_para.b1,cali_raw_para.temph,cali_raw_para.templ);
								frame_send_string(str);
								if(!autoagj)
								{	
									NRF_LOG_INFO("calibrate wrong, normal mode");
									sm_jump(SM_ERROR, 12);
									motor_start_once(20);
									return;
								}
								//传感器值应该在一个范围中
								if (   (cali_raw_para.d0 > 50)
									|| (cali_raw_para.g0 < 2600) || (cali_raw_para.g0 > 3300)//新算法有改动
									|| (cali_raw_para.b0 < 2600) || (cali_raw_para.b0 > 3300) //3100--》3300
									|| (cali_raw_para.g1 > 200)//新算法有改动
									|| (cali_raw_para.b1 > 200))//新算法有改动
								{
										NRF_LOG_INFO("calibrate wrong, normal mode");
										sm_jump(SM_ERROR, 12);
										motor_start_once(20);
										return;
							
								}
												//漏光严重
								else if ((lg > 200) || (lb > 200))//新算法有改动
								{
										NRF_LOG_INFO("calibrate wrong");
										sm_jump(SM_ERROR, 12);
										motor_start_once(20);
										return;
								}
								
								stage = 3;
								page_calibrate_ok();
								motor_start_once(20);
								cali_raw_para.is_valid = cali_mode;
								cali_raw_para.result_ratio = 100;//默认比率为1.0
								if(cali_raw_para.g0 > 3040)       //新算法有改动 这里是设置预期的区间
								{
									 cali_raw_para.g_pwm = cali_raw_para.g_pwm -(uint32_t)((cali_raw_para.g0-3000)/10);//新算法有修改
								}
								if((cali_raw_para.g0 < 2960))//新算法有修改
								{
										cali_raw_para.g_pwm = cali_raw_para.g_pwm + (uint32_t)((3000 - cali_raw_para.g0)/10);//新算法有修改
								}

								
								if(cali_raw_para.b0 > 3040)
								{
									 cali_raw_para.b_pwm = cali_raw_para.b_pwm- (uint32_t)((cali_raw_para.b0-3000)/10);
								}
								if((cali_raw_para.b0 < 2960))
								{
										cali_raw_para.b_pwm = cali_raw_para.b_pwm + (uint32_t)((3000 - cali_raw_para.b0)/10);
								}
								storage_update_cali_para(&cali_raw_para);//新算法修改测试
								
								//snprintf(str, sizeof(str), "zero: %d, %d, %d, air: %d, %d, %d,, %d", cali_para.d0, cali_para.g0, cali_para.b0, cali_para.d1, cali_para.g1, cali_para.b1, cali_para.result_ratio);
								snprintf(str, sizeof(str), "g_pwm: %d, b_pwm: %d", cali_raw_para.g_pwm,cali_raw_para.b_pwm);
								frame_send_string(str);

								algo_init();
								
								//发送给APP/上位机
								protocol_ble_send_cali_data(&cali_raw_para);
//				
//				//发送算法结果调整比率值
//				nus_data_t data;
//				data.payload.cmd = 0x1B;	//指令号
//				data.payload.para_len = 4;	//表示para的长度
//				data.payload.para[0] = cali_raw_para.result_ratio;
//				data.payload.para[1] = 0;
//				data.payload.para[2] = 0;
//				data.payload.para[3] = 0;
//				data.payload_len = 2 + data.payload.para_len;
//				frame_send_frame(&data);
				}
				else
				{
						page_cali_cnt(average_count, CALI_AVERAGE_TIMES);
				}
		}	
  }
}
