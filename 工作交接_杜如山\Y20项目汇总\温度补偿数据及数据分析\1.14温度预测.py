import pandas as pd
import matplotlib.pyplot as plt
from sklearn.linear_model import LinearRegression
import numpy as np

# 读取Excel文件
df = pd.read_excel('1.13温度梯度.xlsx')

# 提取温度、8个仪器的G值和B值数据
temperatures = df['Temperature']
g_values = df[['Instrument1_G', 'Instrument2_G', 'Instrument3_G', 'Instrument4_G', 'Instrument5_G', 'Instrument6_G', 'Instrument7_G', 'Instrument8_G']]
b_values = df[['Instrument1_B', 'Instrument2_B', 'Instrument3_B', 'Instrument4_B', 'Instrument5_B', 'Instrument6_B', 'Instrument7_B', 'Instrument8_B']]

# 创建一个新的DataFrame
data = pd.DataFrame({
    'Temperature': temperatures,
    'G_Instrument1_G': g_values['Instrument1_G'],
    'G_Instrument2_G': g_values['Instrument2_G'],
    'G_Instrument3_G': g_values['Instrument3_G'],
    'G_Instrument4_G': g_values['Instrument4_G'],
    'G_Instrument5_G': g_values['Instrument5_G'],
    'G_Instrument6_G': g_values['Instrument6_G'],
    'G_Instrument7_G': g_values['Instrument7_G'],
    'G_Instrument8_G': g_values['Instrument8_G'],
    'B_Instrument1_B': b_values['Instrument1_B'],
    'B_Instrument2_B': b_values['Instrument2_B'],
    'B_Instrument3_B': b_values['Instrument3_B'],
    'B_Instrument4_B': b_values['Instrument4_B'],
    'B_Instrument5_B': b_values['Instrument5_B'],
    'B_Instrument6_B': b_values['Instrument6_B'],
    'B_Instrument7_B': b_values['Instrument7_B'],
    'B_Instrument8_B': b_values['Instrument8_B'],
})

# 计算每个温度点的平均G值和B值
data['Average_G'] = data[['G_Instrument1_G', 'G_Instrument2_G', 'G_Instrument3_G', 'G_Instrument4_G', 'G_Instrument5_G', 'G_Instrument6_G', 'G_Instrument7_G', 'G_Instrument8_G']].mean(axis=1)
data['Average_B'] = data[['B_Instrument1_B', 'B_Instrument2_B', 'B_Instrument3_B', 'B_Instrument4_B', 'B_Instrument5_B', 'B_Instrument6_B', 'B_Instrument7_B', 'B_Instrument8_B']].mean(axis=1)

# 定义温度区间
temperature_ranges = [(10, 15), (15, 20), (20, np.inf)]

# 创建图形和轴
plt.figure(figsize=(12, 8))

# 对每个温度区间进行线性回归拟合并绘制散点图和回归曲线
for i, temp_range in enumerate(temperature_ranges):
    # 筛选出当前温度区间的数据
    if temp_range[1] == np.inf:
        filtered_data = data[data['Temperature'] >= temp_range[0]]
    else:
        filtered_data = data[(data['Temperature'] >= temp_range[0]) & (data['Temperature'] <= temp_range[1])]
    
    # 检查筛选后的数据集是否为空
    if filtered_data.empty:
        print(f"Temperature range {temp_range} has no data, skipping.")
        continue
    
    # 准备线性回归模型的数据
    X_g = filtered_data[['Average_G']]
    y_g = filtered_data['Temperature']
    X_b = filtered_data[['Average_B']]
    y_b = filtered_data['Temperature']
    
    # 创建线性回归模型
    model_g = LinearRegression()
    model_g.fit(X_g, y_g)
    model_b = LinearRegression()
    model_b.fit(X_b, y_b)
    
    # 获取系数和截距
    coef_g = model_g.coef_[0]
    intercept_g = model_g.intercept_
    coef_b = model_b.coef_[0]
    intercept_b = model_b.intercept_
    
    # 输出线性回归系数和截距
    print(f"Temperature range {temp_range} - G value regression coefficient: {coef_g:.4f}, intercept: {intercept_g:.4f}")
    # print(f"Temperature range {temp_range} - B value regression coefficient: {coef_b:.4f}, intercept: {intercept_b:.4f}")
    
    # 绘制G值散点图和回归曲线
    plt.scatter(filtered_data['Average_G'], filtered_data['Temperature'], label=f'Temperature Range {temp_range} (G)')
    g_range = np.linspace(filtered_data['Average_G'].min(), filtered_data['Average_G'].max(), 100)
    plt.plot(g_range, model_g.predict(np.array([g_range]).T), label=f'Regression Line {temp_range} (G)', linestyle='--')
    
    # # 绘制B值散点图和回归曲线
    # plt.scatter(filtered_data['Average_B'], filtered_data['Temperature'], label=f'Temperature Range {temp_range} (B)', marker='x')
    # b_range = np.linspace(filtered_data['Average_B'].min(), filtered_data['Average_B'].max(), 100)
    # plt.plot(b_range, model_b.predict(np.array([b_range]).T), label=f'Regression Line {temp_range} (B)', linestyle=':')

# 添加标题和标签
plt.title('Linear Regression of Temperature vs Average G Values by Temperature Range')
plt.xlabel('Average Value')
plt.ylabel('Temperature (°C)')
plt.legend()
plt.grid(True)
plt.show()

import numpy as np

# 定义温度区间的线性回归系数和截距
temperature_ranges = [(10, 15), (15, 20), (20, np.inf)]
coefficients = [-0.3449, -0.1508, -0.1469]
intercepts = [185.5723, 89.6286, 86.9757]

def predict_temperature(g_value):
    # 遍历每个温度区间
    for i, temp_range in enumerate(temperature_ranges):
        # 计算预测温度
        predicted_temp = coefficients[i] * g_value + intercepts[i]
        # 检查预测温度是否在当前区间内
        if temp_range[0] <= predicted_temp <= temp_range[1]:
            return predicted_temp
    # 如果没有合适的区间，返回None
    return None
