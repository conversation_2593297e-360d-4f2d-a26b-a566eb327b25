#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
使用示例：如何使用数据分类器处理其他文件
"""

from process_10_20_files import DataClassifier
import os

def process_custom_files(file_paths):
    """处理自定义文件列表"""
    classifier = DataClassifier()
    
    print("开始处理自定义文件...")
    print("-" * 40)
    
    # 处理文件
    total_processed = classifier.process_files(file_paths)
    
    if total_processed > 0:
        # 打印摘要
        classifier.print_summary()
        
        # 保存结果
        output_dir = "custom_results"
        if not os.path.exists(output_dir):
            os.makedirs(output_dir)
        
        # 保存到Excel
        excel_path = os.path.join(output_dir, "custom_classified_data.xlsx")
        classifier.save_to_excel(excel_path)
        
        # 保存格式化文本
        text_path = os.path.join(output_dir, "custom_formatted_data.txt")
        classifier.save_formatted_text(text_path)
        
        # 生成图表
        chart_files = classifier.generate_charts(output_dir)
        
        print(f"\n处理完成！结果保存在 {output_dir} 目录中")
        print("生成的文件:")
        print(f"  - Excel文件: {os.path.basename(excel_path)}")
        print(f"  - 文本文件: {os.path.basename(text_path)}")
        for chart_file in chart_files:
            print(f"  - 图表文件: {os.path.basename(chart_file)}")
    else:
        print("没有找到有效数据进行处理")

def process_single_file_example():
    """处理单个文件的示例"""
    classifier = DataClassifier()
    
    # 假设我们要处理一个新文件
    test_file = "test_data.txt"
    
    # 创建一个测试文件
    with open(test_file, 'w', encoding='utf-8') as f:
        f.write("""校准：
G:3000，B:2800   GPWM=150    BPWM=55

白板：
Got BLE CMD49
色板CH:1   D:5   G:3500   B:3600   R:1.50 mgDL   257

皮肤1：
Got BLE CMD49
色板CH:1   D:3   G:1200   B:600   R:5.20 mgDL   257
Got BLE CMD19
色板CH:1   D:3   G:1150   B:580   R:5.50 mgDL   257
""")
    
    print(f"创建测试文件: {test_file}")
    print("文件内容包含校准、白板、皮肤1数据")
    
    # 处理文件
    count = classifier.process_file(test_file)
    print(f"处理完成，提取了 {count} 条数据")
    
    # 显示结果
    for category, data_list in classifier.classified_data.items():
        if len(data_list) > 0:
            print(f"\n{category}:")
            for entry in data_list:
                print(f"  {entry['formatted_gbr']}")
    
    # 清理测试文件
    if os.path.exists(test_file):
        os.remove(test_file)
        print(f"\n已删除测试文件: {test_file}")

def show_format_examples():
    """显示支持的数据格式示例"""
    print("支持的数据格式示例:")
    print("=" * 50)
    
    classifier = DataClassifier()
    
    examples = [
        ("标准色板格式", "色板CH:1   D:4   G:2712   B:2725   R:2.72 mgDL   257"),
        ("校准数据格式", "G:2893，B:2643   GPWM=148    BPWM=53"),
        ("需要过滤的命令1", "Got BLE CMD49"),
        ("需要过滤的命令2", "Got BLE CMD19"),
        ("分类标签", "皮肤1："),
    ]
    
    for desc, example in examples:
        print(f"\n{desc}:")
        print(f"  原始: {example}")
        
        if classifier.should_filter_line(example):
            print("  结果: [已过滤]")
        else:
            result = classifier.extract_gbr_values(example)
            if result:
                print(f"  结果: {result}")
            else:
                print("  结果: [分类标签，无数据提取]")

def main():
    """主函数"""
    print("数据分类器使用示例")
    print("=" * 60)
    
    # 显示格式示例
    show_format_examples()
    
    print("\n" + "=" * 60)
    
    # 单文件处理示例
    process_single_file_example()
    
    print("\n" + "=" * 60)
    print("如需处理其他文件，请调用:")
    print("process_custom_files(['your_file1.txt', 'your_file2.txt'])")

if __name__ == "__main__":
    main()
