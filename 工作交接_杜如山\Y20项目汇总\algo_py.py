import matplotlib.pyplot as plt
import numpy as np

# 假设的常量值
mt0 = 3000  # 标准温度
blue = 560    # 测得B值
green = 520   # 测得G值

# 定义函数
def calculate_mt(tempture):
    if 500 <= tempture <= 5000:
        mt = (tempture - mt0) / 100 #t=3000 mt = 5
        if mt < 0:
            mb = blue / (1 - 0.006118 * mt) 
            mg = green / (1 - 0.01798 * mt)#0.02298 
        else:
            mb = blue / (1 - 0.002088 * mt)#blue/(1-0.002088*5)
            mg = green / (1 - 0.012798 * mt)
        return mt, mb, mg
    else:
        return 0, 0, 0

# 生成温度数据
temptures = np.linspace(500, 5000, 1000)  # 生成0到6000之间的1000个点
mts = []
mbs = []
mgs = []

# 计算每个温度下的mt, mb, mg
for tempture in temptures:
    mt, mb, mg = calculate_mt(tempture)
    mts.append(mt)
    mbs.append(mb)
    mgs.append(mg)

# 绘制mb和mg随tempture变化的图像
plt.figure(figsize=(10, 6))
plt.plot(temptures/100, mbs, label='mb')
plt.plot(temptures/100, mgs, label='mg')

# 标注10度，25度，50度的数据
degrees = [10,15, 20, 25 ,49]
for degree in degrees:
    tempture = degree * 100
    mt, mb, mg = calculate_mt(tempture)
    plt.scatter(degree, mb, color='red')  # 标注mb
    plt.scatter(degree, mg, color='green')  # 标注mg
    plt.text(degree, mb, f'({degree}°C, {mb:.2f})', fontsize=9, verticalalignment='bottom', color='red')
    plt.text(degree, mg, f'({degree}°C, {mg:.2f})', fontsize=9, verticalalignment='top', color='green')

plt.title('mb and mg vs Tempture')
plt.xlabel('Tempture (°C)')
plt.ylabel('Value')
plt.axhline(0, color='black',linewidth=0.5)
plt.axvline(0, color='black',linewidth=0.5)
plt.grid(color = 'gray', linestyle = '--', linewidth = 0.5)
plt.legend()
plt.show()