#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据分类和格式转换脚本
功能：
1. 数据分类：校准，白板，皮肤1，皮肤2，皮肤3，皮肤4，皮肤5
2. 过滤：过滤"Got BLE CMD49"和"Got BLE CMD19"
3. 格式转换：将"色板CH:1   D:4   G:2712   B:2725   R:2.72 mgDL   257"转换为"G:2712  B:2725  R:2.72"
"""

import os
import re
import pandas as pd
from datetime import datetime


class DataClassifier:
    """数据分类和处理类"""
    
    def __init__(self):
        # 定义数据分类
        self.categories = ['校准', '白板', '皮肤1', '皮肤2', '皮肤3', '皮肤4', '皮肤5']
        
        # 需要过滤的命令
        self.filter_commands = ['Got BLE CMD49', 'Got BLE CMD19']
        
        # 数据存储
        self.classified_data = {category: [] for category in self.categories}
        self.classified_data['其他'] = []  # 添加其他分类
        self.raw_data = []
        
    def should_filter_line(self, line):
        """判断是否应该过滤该行"""
        for filter_cmd in self.filter_commands:
            if filter_cmd in line:
                return True
        return False
    
    def extract_gbr_values(self, line):
        """从不同格式的行中提取G、B、R值"""
        # 格式1: "色板CH:1   D:4   G:2712   B:2725   R:2.72 mgDL   257"
        pattern1 = r'色板CH:\d+\s+D:\d+\s+G:(\d+)\s+B:(\d+)\s+R:([\d.]+)\s+mgDL'
        match1 = re.search(pattern1, line)
        if match1:
            g_val, b_val, r_val = match1.groups()
            return f"G:{g_val}  B:{b_val}  R:{r_val}"
        
        # 格式2: "G:2893，B:2643   GPWM=148    BPWM=53" (校准数据格式)
        pattern2 = r'G:(\d+)[，,]\s*B:(\d+)'
        match2 = re.search(pattern2, line)
        if match2:
            g_val, b_val = match2.groups()
            return f"G:{g_val}  B:{b_val}  R:N/A"
        
        return None
    
    def extract_numeric_values(self, formatted_gbr):
        """从格式化的GBR字符串中提取数值"""
        try:
            # 解析 "G:2712  B:2725  R:2.72" 格式
            parts = formatted_gbr.split()
            g_val = float(parts[0].split(':')[1])
            b_val = float(parts[1].split(':')[1])
            r_part = parts[2].split(':')[1]
            r_val = float(r_part) if r_part != 'N/A' else None
            return g_val, b_val, r_val
        except:
            return None, None, None
    
    def process_file(self, file_path):
        """处理单个文件"""
        filename = os.path.basename(file_path)
        print(f"正在处理文件: {filename}")
        
        current_category = None
        processed_count = 0
        
        try:
            with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
                lines = f.readlines()
            
            for line_num, line in enumerate(lines, 1):
                line = line.strip()
                if not line:
                    continue
                
                # 检测分类标签
                for category in self.categories:
                    if line.startswith(category + '：') or line.startswith(category + ':'):
                        current_category = category
                        print(f"  发现分类: {category}")
                        break
                
                # 过滤不需要的命令
                if self.should_filter_line(line):
                    continue
                
                # 提取G、B、R值
                gbr_formatted = self.extract_gbr_values(line)
                if gbr_formatted and current_category:
                    # 提取数值用于分析
                    g_val, b_val, r_val = self.extract_numeric_values(gbr_formatted)
                    
                    data_entry = {
                        'file': filename,
                        'line_number': line_num,
                        'category': current_category,
                        'original_line': line,
                        'formatted_gbr': gbr_formatted,
                        'g_value': g_val,
                        'b_value': b_val,
                        'r_value': r_val,
                        'timestamp': datetime.now().isoformat()
                    }
                    
                    self.classified_data[current_category].append(data_entry)
                    self.raw_data.append(data_entry)
                    processed_count += 1
        
        except Exception as e:
            print(f"处理文件 {file_path} 时出错: {str(e)}")
        
        print(f"  处理完成，共提取 {processed_count} 条有效数据")
        return processed_count
    
    def process_files(self, file_paths):
        """处理多个文件"""
        total_processed = 0
        for file_path in file_paths:
            if os.path.exists(file_path):
                count = self.process_file(file_path)
                total_processed += count
            else:
                print(f"文件不存在: {file_path}")
        
        print(f"\n总共处理了 {total_processed} 条数据")
        return total_processed
    
    def print_summary(self):
        """打印数据摘要"""
        print("\n" + "="*60)
        print("数据分类摘要")
        print("="*60)

        # 按文件统计
        print("按文件统计:")
        for file_name in ['10.txt', '20.txt']:
            file_count = sum(1 for entry in self.raw_data if entry['file'] == file_name)
            print(f"  {file_name}: {file_count} 条数据")
        print()

        # 按分类统计
        print("按分类统计:")
        for category in self.categories:
            count = len(self.classified_data[category])
            print(f"  {category}: {count} 条数据")

            # 按文件细分
            if count > 0:
                file_10_count = sum(1 for entry in self.classified_data[category] if entry['file'] == '10.txt')
                file_20_count = sum(1 for entry in self.classified_data[category] if entry['file'] == '20.txt')
                print(f"    - 10.txt: {file_10_count} 条")
                print(f"    - 20.txt: {file_20_count} 条")

                # 显示前3条格式化后的数据
                print("    示例数据:")
                for i, entry in enumerate(self.classified_data[category][:3]):
                    print(f"      {i+1}. [{entry['file']}] {entry['formatted_gbr']}")
                if count > 3:
                    print(f"      ... 还有 {count-3} 条数据")
            print()
    
    def save_to_excel(self, output_path):
        """保存数据到Excel文件"""
        try:
            with pd.ExcelWriter(output_path, engine='openpyxl') as writer:
                # 创建汇总表
                all_data = []
                for entry in self.raw_data:
                    all_data.append({
                        '文件名': entry['file'],
                        '行号': entry['line_number'],
                        '分类': entry['category'],
                        '格式化数据': entry['formatted_gbr'],
                        'G值': entry['g_value'],
                        'B值': entry['b_value'],
                        'R值': entry['r_value'],
                        '原始数据': entry['original_line']
                    })

                df_all = pd.DataFrame(all_data)
                df_all.to_excel(writer, sheet_name='汇总数据', index=False)

                # 按文件分别创建工作表
                for file_name in ['10.txt', '20.txt']:
                    file_data = []
                    for entry in self.raw_data:
                        if entry['file'] == file_name:
                            file_data.append({
                                '行号': entry['line_number'],
                                '分类': entry['category'],
                                '格式化数据': entry['formatted_gbr'],
                                'G值': entry['g_value'],
                                'B值': entry['b_value'],
                                'R值': entry['r_value'],
                                '原始数据': entry['original_line']
                            })

                    if file_data:
                        df_file = pd.DataFrame(file_data)
                        sheet_name = file_name.replace('.txt', '文件数据')
                        df_file.to_excel(writer, sheet_name=sheet_name, index=False)

                # 为每个分类创建单独的工作表
                for category in self.categories:
                    if len(self.classified_data[category]) > 0:
                        category_data = []
                        for entry in self.classified_data[category]:
                            category_data.append({
                                '文件名': entry['file'],
                                '行号': entry['line_number'],
                                '格式化数据': entry['formatted_gbr'],
                                'G值': entry['g_value'],
                                'B值': entry['b_value'],
                                'R值': entry['r_value'],
                                '原始数据': entry['original_line']
                            })

                        df_category = pd.DataFrame(category_data)
                        df_category.to_excel(writer, sheet_name=category, index=False)

            print(f"数据已保存到Excel文件: {output_path}")

        except Exception as e:
            print(f"保存Excel文件时出错: {str(e)}")

    def save_to_result_excel(self, result_path):
        """将数据填写到result.xlsx的指定区域，并添加颜色和边框"""
        try:
            import openpyxl
            from openpyxl.styles import PatternFill, Border, Side, Font

            # 打开现有的result.xlsx文件
            wb = openpyxl.load_workbook(result_path)
            ws = wb['Whole data']

            # 定义颜色和样式
            colors = {
                '白板': 'E6F3FF',      # 浅蓝色
                '校准': 'E6FFE6',      # 浅绿色
                '皮肤1': 'FFE6E6',     # 浅红色
                '皮肤2': 'FFF0E6',     # 浅橙色
                '皮肤3': 'FFFFE6',     # 浅黄色
                '皮肤4': 'F0E6FF',     # 浅紫色
                '皮肤5': 'E6F0FF'      # 浅蓝紫色
            }

            # 定义边框样式
            thin_border = Border(
                left=Side(style='thin'),
                right=Side(style='thin'),
                top=Side(style='thin'),
                bottom=Side(style='thin')
            )

            # 定义字体样式
            data_font = Font(name='Arial', size=10, bold=False)
            header_font = Font(name='Arial', size=10, bold=True)

            # 获取每个分类的具体数据（不是平均值，而是每条数据）
            # 皮肤1对应列5-14（标题为"1"），皮肤2对应列15-24（标题为"2"），以此类推
            skin_categories = ['皮肤1', '皮肤2', '皮肤3', '皮肤4', '皮肤5']

            for i, category in enumerate(skin_categories):
                # 计算起始列：皮肤1从第5列开始，皮肤2从第15列开始，每个分类占10列
                start_col = 5 + i * 10

                # 创建颜色填充
                fill = PatternFill(start_color=colors[category], end_color=colors[category], fill_type='solid')

                # 获取10.txt的数据
                data_10 = [entry for entry in self.classified_data[category] if entry['file'] == '10.txt']
                # 获取20.txt的数据
                data_20 = [entry for entry in self.classified_data[category] if entry['file'] == '20.txt']

                # 填写10.txt的数据到对应列
                for j, entry in enumerate(data_10[:10]):  # 最多10条数据
                    col = start_col + j
                    if entry['g_value'] is not None:
                        cell = ws.cell(row=2, column=col, value=int(entry['g_value']))  # G-10
                        cell.fill = fill
                        cell.border = thin_border
                        cell.font = data_font
                    if entry['b_value'] is not None:
                        cell = ws.cell(row=3, column=col, value=int(entry['b_value']))  # B-10
                        cell.fill = fill
                        cell.border = thin_border
                        cell.font = data_font
                    if entry['r_value'] is not None:
                        cell = ws.cell(row=4, column=col, value=round(entry['r_value'], 2))  # R-10
                        cell.fill = fill
                        cell.border = thin_border
                        cell.font = data_font

                # 填写20.txt的数据到对应列
                for j, entry in enumerate(data_20[:10]):  # 最多10条数据
                    col = start_col + j
                    if entry['g_value'] is not None:
                        cell = ws.cell(row=5, column=col, value=int(entry['g_value']))  # G-20
                        cell.fill = fill
                        cell.border = thin_border
                        cell.font = data_font
                    if entry['b_value'] is not None:
                        cell = ws.cell(row=6, column=col, value=int(entry['b_value']))  # B-20
                        cell.fill = fill
                        cell.border = thin_border
                        cell.font = data_font
                    if entry['r_value'] is not None:
                        cell = ws.cell(row=7, column=col, value=round(entry['r_value'], 2))  # R-20
                        cell.fill = fill
                        cell.border = thin_border
                        cell.font = data_font

            # 处理白板和校准数据（填写到第2、3列）
            # 白板数据
            white_fill = PatternFill(start_color=colors['白板'], end_color=colors['白板'], fill_type='solid')
            if len(self.classified_data['白板']) > 0:
                for entry in self.classified_data['白板']:
                    if entry['file'] == '10.txt':
                        if entry['g_value'] is not None:
                            cell = ws.cell(row=2, column=2, value=int(entry['g_value']))  # G-10白板
                            cell.fill = white_fill
                            cell.border = thin_border
                            cell.font = data_font
                        if entry['b_value'] is not None:
                            cell = ws.cell(row=3, column=2, value=int(entry['b_value']))  # B-10白板
                            cell.fill = white_fill
                            cell.border = thin_border
                            cell.font = data_font
                        if entry['r_value'] is not None:
                            cell = ws.cell(row=4, column=2, value=round(entry['r_value'], 2))  # R-10白板
                            cell.fill = white_fill
                            cell.border = thin_border
                            cell.font = data_font
                    elif entry['file'] == '20.txt':
                        if entry['g_value'] is not None:
                            cell = ws.cell(row=5, column=2, value=int(entry['g_value']))  # G-20白板
                            cell.fill = white_fill
                            cell.border = thin_border
                            cell.font = data_font
                        if entry['b_value'] is not None:
                            cell = ws.cell(row=6, column=2, value=int(entry['b_value']))  # B-20白板
                            cell.fill = white_fill
                            cell.border = thin_border
                            cell.font = data_font
                        if entry['r_value'] is not None:
                            cell = ws.cell(row=7, column=2, value=round(entry['r_value'], 2))  # R-20白板
                            cell.fill = white_fill
                            cell.border = thin_border
                            cell.font = data_font

            # 校准数据
            calib_fill = PatternFill(start_color=colors['校准'], end_color=colors['校准'], fill_type='solid')
            if len(self.classified_data['校准']) > 0:
                for entry in self.classified_data['校准']:
                    if entry['file'] == '10.txt':
                        if entry['g_value'] is not None:
                            cell = ws.cell(row=2, column=3, value=int(entry['g_value']))  # G-10校准
                            cell.fill = calib_fill
                            cell.border = thin_border
                            cell.font = data_font
                        if entry['b_value'] is not None:
                            cell = ws.cell(row=3, column=3, value=int(entry['b_value']))  # B-10校准
                            cell.fill = calib_fill
                            cell.border = thin_border
                            cell.font = data_font
                        if entry['r_value'] is not None:
                            cell = ws.cell(row=4, column=3, value=round(entry['r_value'], 2))  # R-10校准
                            cell.fill = calib_fill
                            cell.border = thin_border
                            cell.font = data_font
                    elif entry['file'] == '20.txt':
                        if entry['g_value'] is not None:
                            cell = ws.cell(row=5, column=3, value=int(entry['g_value']))  # G-20校准
                            cell.fill = calib_fill
                            cell.border = thin_border
                            cell.font = data_font
                        if entry['b_value'] is not None:
                            cell = ws.cell(row=6, column=3, value=int(entry['b_value']))  # B-20校准
                            cell.fill = calib_fill
                            cell.border = thin_border
                            cell.font = data_font
                        if entry['r_value'] is not None:
                            cell = ws.cell(row=7, column=3, value=round(entry['r_value'], 2))  # R-20校准
                            cell.fill = calib_fill
                            cell.border = thin_border
                            cell.font = data_font

            # 格式化标题行和行标签
            header_fill = PatternFill(start_color='D0D0D0', end_color='D0D0D0', fill_type='solid')  # 灰色

            # 格式化第1行标题
            for col in range(1, 55):  # 假设最多到54列
                cell = ws.cell(row=1, column=col)
                if cell.value:
                    cell.fill = header_fill
                    cell.border = thin_border
                    cell.font = header_font

            # 格式化第1列的行标签
            for row in range(2, 8):  # G-10, B-10, R-10, G-20, B-20, R-20
                cell = ws.cell(row=row, column=1)
                if cell.value:
                    cell.fill = header_fill
                    cell.border = thin_border
                    cell.font = header_font

            # 添加数据区域的整体边框
            # 为所有数据单元格添加边框（即使是空单元格）
            for row in range(2, 8):
                for col in range(2, 55):
                    cell = ws.cell(row=row, column=col)
                    if not cell.border.left.style:  # 如果还没有边框
                        cell.border = thin_border

            # 添加折线图
            self._add_charts_to_excel(wb, ws)

            # 保存文件
            wb.save(result_path)
            print(f"数据已填写到result.xlsx文件: {result_path}")
            print("已添加颜色区分和边框格式:")
            print("  - 白板数据: 浅蓝色")
            print("  - 校准数据: 浅绿色")
            print("  - 皮肤1数据: 浅红色")
            print("  - 皮肤2数据: 浅橙色")
            print("  - 皮肤3数据: 浅黄色")
            print("  - 皮肤4数据: 浅紫色")
            print("  - 皮肤5数据: 浅蓝紫色")
            print("  - 标题和标签: 灰色")
            print("已添加5种皮肤的GBR折线图")

        except Exception as e:
            print(f"填写result.xlsx文件时出错: {str(e)}")

    def _add_charts_to_excel(self, wb, ws):
        """为5种皮肤添加GBR折线图"""
        try:
            from openpyxl.chart import LineChart, Reference

            skin_categories = ['皮肤1', '皮肤2', '皮肤3', '皮肤4', '皮肤5']

            for i, category in enumerate(skin_categories):
                # 计算数据列范围：皮肤1从第5列开始，每个分类占10列
                start_col = 5 + i * 10
                end_col = start_col + 9

                # 检查是否有数据
                has_data = False
                for row in range(2, 8):
                    for col in range(start_col, end_col + 1):
                        if ws.cell(row=row, column=col).value is not None:
                            has_data = True
                            break
                    if has_data:
                        break

                if not has_data:
                    continue

                # 创建折线图
                chart = LineChart()
                chart.title = f"{category} GBR数据对比"
                chart.x_axis.title = "测量序号"
                chart.y_axis.title = "数值"
                chart.width = 15
                chart.height = 10

                # 分别添加每行数据作为一个系列
                series_names = ["10.txt G值", "10.txt B值", "10.txt R值", "20.txt G值", "20.txt B值", "20.txt R值"]

                for row_idx, series_name in enumerate(series_names):
                    row = 2 + row_idx
                    data = Reference(ws, min_col=start_col, max_col=end_col, min_row=row, max_row=row)
                    chart.add_data(data, titles_from_data=False)

                    # 设置系列名称
                    if len(chart.series) > row_idx:
                        try:
                            chart.series[row_idx].title = series_name
                        except:
                            pass

                # 添加图例
                chart.legend.position = "r"  # 右侧

                # 将图表添加到工作表
                # 计算图表位置：每个图表占用25行，从第10行开始
                chart_row = 10 + i * 25
                ws.add_chart(chart, f"A{chart_row}")

            print(f"已为5种皮肤添加GBR折线图")

        except Exception as e:
            print(f"添加图表时出错: {str(e)}")






    
    def save_formatted_text(self, output_path):
        """保存格式化后的文本文件"""
        try:
            with open(output_path, 'w', encoding='utf-8') as f:
                f.write("数据分类和格式转换结果\n")
                f.write("="*50 + "\n\n")

                # 按文件分组显示
                for file_name in ['10.txt', '20.txt']:
                    f.write(f"=== {file_name} 文件数据 ===\n\n")

                    for category in self.categories:
                        category_entries = [entry for entry in self.classified_data[category]
                                          if entry['file'] == file_name]
                        if category_entries:
                            f.write(f"{category} ({len(category_entries)} 条):\n")
                            for entry in category_entries:
                                f.write(f"  {entry['formatted_gbr']}\n")
                            f.write("\n")
                    f.write("\n")

                # 按分类汇总显示
                f.write("=== 按分类汇总 ===\n\n")
                for category in self.categories:
                    if len(self.classified_data[category]) > 0:
                        f.write(f"{category} (总计 {len(self.classified_data[category])} 条):\n")
                        for entry in self.classified_data[category]:
                            f.write(f"  [{entry['file']}] {entry['formatted_gbr']}\n")
                        f.write("\n")

            print(f"格式化文本已保存到: {output_path}")

        except Exception as e:
            print(f"保存文本文件时出错: {str(e)}")




def main():
    """主函数"""
    # 创建数据分类器
    classifier = DataClassifier()

    # 要处理的文件
    files_to_process = ['10.txt', '20.txt']

    print("开始处理数据文件...")
    print("-" * 40)

    # 处理文件
    classifier.process_files(files_to_process)

    # 打印摘要
    classifier.print_summary()

    # 保存结果
    output_dir = "processed_results"
    if not os.path.exists(output_dir):
        os.makedirs(output_dir)

    # 保存到Excel
    excel_path = os.path.join(output_dir, "classified_data.xlsx")
    classifier.save_to_excel(excel_path)

    # 保存格式化文本
    text_path = os.path.join(output_dir, "formatted_data.txt")
    classifier.save_formatted_text(text_path)

    # 填写到result.xlsx
    result_path = os.path.join(output_dir, "result.xlsx")
    if os.path.exists(result_path):
        classifier.save_to_result_excel(result_path)
    else:
        print(f"未找到result.xlsx文件: {result_path}")

    print(f"\n处理完成！结果保存在 {output_dir} 目录中")
    print("生成的文件:")
    print(f"  - Excel文件: {os.path.basename(excel_path)}")
    print(f"  - 文本文件: {os.path.basename(text_path)}")
    if os.path.exists(result_path):
        print(f"  - 结果文件: {os.path.basename(result_path)} (已填写数据)")


if __name__ == "__main__":
    main()
