#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据分类和格式转换脚本
功能：
1. 数据分类：校准，白板，皮肤1，皮肤2，皮肤3，皮肤4，皮肤5
2. 过滤：过滤"Got BLE CMD49"和"Got BLE CMD19"
3. 格式转换：将"色板CH:1   D:4   G:2712   B:2725   R:2.72 mgDL   257"转换为"G:2712  B:2725  R:2.72"
"""

import os
import re
import pandas as pd
from datetime import datetime


class DataClassifier:
    """数据分类和处理类"""
    
    def __init__(self):
        # 定义数据分类
        self.categories = ['校准', '白板', '皮肤1', '皮肤2', '皮肤3', '皮肤4', '皮肤5']
        
        # 需要过滤的命令
        self.filter_commands = ['Got BLE CMD49', 'Got BLE CMD19']
        
        # 数据存储
        self.classified_data = {category: [] for category in self.categories}
        self.classified_data['其他'] = []  # 添加其他分类
        self.raw_data = []
        
    def should_filter_line(self, line):
        """判断是否应该过滤该行"""
        for filter_cmd in self.filter_commands:
            if filter_cmd in line:
                return True
        return False
    
    def extract_gbr_values(self, line):
        """从不同格式的行中提取G、B、R值"""
        # 格式1: "色板CH:1   D:4   G:2712   B:2725   R:2.72 mgDL   257"
        pattern1 = r'色板CH:\d+\s+D:\d+\s+G:(\d+)\s+B:(\d+)\s+R:([\d.]+)\s+mgDL'
        match1 = re.search(pattern1, line)
        if match1:
            g_val, b_val, r_val = match1.groups()
            return f"G:{g_val}  B:{b_val}  R:{r_val}"
        
        # 格式2: "G:2893，B:2643   GPWM=148    BPWM=53" (校准数据格式)
        pattern2 = r'G:(\d+)[，,]\s*B:(\d+)'
        match2 = re.search(pattern2, line)
        if match2:
            g_val, b_val = match2.groups()
            return f"G:{g_val}  B:{b_val}  R:N/A"
        
        return None
    
    def extract_numeric_values(self, formatted_gbr):
        """从格式化的GBR字符串中提取数值"""
        try:
            # 解析 "G:2712  B:2725  R:2.72" 格式
            parts = formatted_gbr.split()
            g_val = float(parts[0].split(':')[1])
            b_val = float(parts[1].split(':')[1])
            r_part = parts[2].split(':')[1]
            r_val = float(r_part) if r_part != 'N/A' else None
            return g_val, b_val, r_val
        except:
            return None, None, None
    
    def process_file(self, file_path):
        """处理单个文件"""
        filename = os.path.basename(file_path)
        print(f"正在处理文件: {filename}")
        
        current_category = None
        processed_count = 0
        
        try:
            with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
                lines = f.readlines()
            
            for line_num, line in enumerate(lines, 1):
                line = line.strip()
                if not line:
                    continue
                
                # 检测分类标签
                for category in self.categories:
                    if line.startswith(category + '：') or line.startswith(category + ':'):
                        current_category = category
                        print(f"  发现分类: {category}")
                        break
                
                # 过滤不需要的命令
                if self.should_filter_line(line):
                    continue
                
                # 提取G、B、R值
                gbr_formatted = self.extract_gbr_values(line)
                if gbr_formatted and current_category:
                    # 提取数值用于分析
                    g_val, b_val, r_val = self.extract_numeric_values(gbr_formatted)
                    
                    data_entry = {
                        'file': filename,
                        'line_number': line_num,
                        'category': current_category,
                        'original_line': line,
                        'formatted_gbr': gbr_formatted,
                        'g_value': g_val,
                        'b_value': b_val,
                        'r_value': r_val,
                        'timestamp': datetime.now().isoformat()
                    }
                    
                    self.classified_data[current_category].append(data_entry)
                    self.raw_data.append(data_entry)
                    processed_count += 1
        
        except Exception as e:
            print(f"处理文件 {file_path} 时出错: {str(e)}")
        
        print(f"  处理完成，共提取 {processed_count} 条有效数据")
        return processed_count
    
    def process_files(self, file_paths):
        """处理多个文件"""
        total_processed = 0
        for file_path in file_paths:
            if os.path.exists(file_path):
                count = self.process_file(file_path)
                total_processed += count
            else:
                print(f"文件不存在: {file_path}")
        
        print(f"\n总共处理了 {total_processed} 条数据")
        return total_processed
    
    def print_summary(self):
        """打印数据摘要"""
        print("\n" + "="*60)
        print("数据分类摘要")
        print("="*60)
        
        for category in self.categories:
            count = len(self.classified_data[category])
            print(f"{category}: {count} 条数据")
            
            # 显示前3条格式化后的数据
            if count > 0:
                print("  示例数据:")
                for i, entry in enumerate(self.classified_data[category][:3]):
                    print(f"    {i+1}. {entry['formatted_gbr']}")
                if count > 3:
                    print(f"    ... 还有 {count-3} 条数据")
            print()
    
    def save_to_excel(self, output_path):
        """保存数据到Excel文件，参照example.xlsx格式"""
        try:
            with pd.ExcelWriter(output_path, engine='openpyxl') as writer:
                # 1. 创建Whole data工作表（参照example.xlsx格式）
                self._create_whole_data_sheet(writer)

                # 2. 创建Green工作表
                self._create_green_sheet(writer)

                # 3. 创建Blue工作表
                self._create_blue_sheet(writer)

                # 4. 创建R工作表
                self._create_r_sheet(writer)

                # 5. 创建原始汇总表（保留原有功能）
                self._create_summary_sheet(writer)

            print(f"数据已保存到Excel文件: {output_path}")

        except Exception as e:
            print(f"保存Excel文件时出错: {str(e)}")

    def _create_whole_data_sheet(self, writer):
        """创建Whole data工作表"""
        # 准备数据结构
        data_dict = {
            'Position/Instrument': ['White borad Value', 'Calibration value', 'PWM']
        }

        # 为每个分类添加G、B、R列
        categories_order = ['校准', '白板', '皮肤1', '皮肤2', '皮肤3', '皮肤4', '皮肤5']

        for i, category in enumerate(categories_order, 1):
            data_dict[f'{i}_G'] = ['', '', '']
            data_dict[f'{i}_B'] = ['', '', '']
            data_dict[f'{i}_R'] = ['', '', '']

        # 填充数据
        for category in self.categories:
            if len(self.classified_data[category]) > 0:
                # 计算平均值
                g_values = [entry['g_value'] for entry in self.classified_data[category] if entry['g_value'] is not None]
                b_values = [entry['b_value'] for entry in self.classified_data[category] if entry['b_value'] is not None]
                r_values = [entry['r_value'] for entry in self.classified_data[category] if entry['r_value'] is not None]

                avg_g = int(sum(g_values) / len(g_values)) if g_values else ''
                avg_b = int(sum(b_values) / len(b_values)) if b_values else ''
                avg_r = round(sum(r_values) / len(r_values), 2) if r_values else ''

                # 确定列索引 - 修正映射关系
                if category == '白板':
                    col_idx = 1  # 白板对应第1列
                elif category == '校准':
                    col_idx = 2  # 校准对应第2列
                elif category in ['皮肤1', '皮肤2', '皮肤3', '皮肤4', '皮肤5']:
                    col_idx = int(category[-1]) + 2  # 皮肤1->3, 皮肤2->4, etc.
                else:
                    continue

                # 填充数据（第1行是白板值，第2行是校准值）
                if category == '白板':
                    data_dict[f'{col_idx}_G'][0] = avg_g
                    data_dict[f'{col_idx}_B'][0] = avg_b
                    data_dict[f'{col_idx}_R'][0] = avg_r
                elif category == '校准':
                    data_dict[f'{col_idx}_G'][1] = avg_g
                    data_dict[f'{col_idx}_B'][1] = avg_b
                    data_dict[f'{col_idx}_R'][1] = avg_r
                else:  # 皮肤数据
                    # 添加新行
                    position_name = category
                    if position_name not in data_dict['Position/Instrument']:
                        # 为所有列添加新行
                        for key in data_dict.keys():
                            data_dict[key].append('')
                        data_dict['Position/Instrument'][-1] = position_name

                    # 找到对应行
                    row_idx = data_dict['Position/Instrument'].index(position_name)
                    data_dict[f'{col_idx}_G'][row_idx] = avg_g
                    data_dict[f'{col_idx}_B'][row_idx] = avg_b
                    data_dict[f'{col_idx}_R'][row_idx] = avg_r

        # 添加皮肤数据行
        for category in ['皮肤1', '皮肤2', '皮肤3', '皮肤4', '皮肤5']:
            if category not in data_dict['Position/Instrument']:
                # 为所有列添加新行
                for key in data_dict.keys():
                    data_dict[key].append('')
                data_dict['Position/Instrument'][-1] = category

        df_whole = pd.DataFrame(data_dict)
        df_whole.to_excel(writer, sheet_name='Whole data', index=False)

    def _create_green_sheet(self, writer):
        """创建Green工作表"""
        data_dict = {
            'Position/Instrument': ['White borad Value', 'Calibration value', 'PWM']
        }

        # 添加各分类的G值列 - 修正顺序：白板、校准、皮肤1-5
        categories_order = ['白板', '校准', '皮肤1', '皮肤2', '皮肤3', '皮肤4', '皮肤5']
        for i, category in enumerate(categories_order, 1):
            data_dict[f'{i}_G'] = ['', '', '']

        # 填充G值数据
        for category in self.categories:
            if len(self.classified_data[category]) > 0:
                g_values = [entry['g_value'] for entry in self.classified_data[category] if entry['g_value'] is not None]
                avg_g = int(sum(g_values) / len(g_values)) if g_values else ''

                if category == '白板':
                    col_idx = 1
                elif category == '校准':
                    col_idx = 2
                elif category in ['皮肤1', '皮肤2', '皮肤3', '皮肤4', '皮肤5']:
                    col_idx = int(category[-1]) + 2
                else:
                    continue

                if category == '白板':
                    data_dict[f'{col_idx}_G'][0] = avg_g
                elif category == '校准':
                    data_dict[f'{col_idx}_G'][1] = avg_g

        # 添加皮肤数据行
        for category in ['皮肤1', '皮肤2', '皮肤3', '皮肤4', '皮肤5']:
            if category not in data_dict['Position/Instrument']:
                for key in data_dict.keys():
                    data_dict[key].append('')
                data_dict['Position/Instrument'][-1] = category

                # 填充该分类的G值
                if len(self.classified_data[category]) > 0:
                    g_values = [entry['g_value'] for entry in self.classified_data[category] if entry['g_value'] is not None]
                    avg_g = int(sum(g_values) / len(g_values)) if g_values else ''
                    col_idx = int(category[-1]) + 2
                    row_idx = len(data_dict['Position/Instrument']) - 1
                    data_dict[f'{col_idx}_G'][row_idx] = avg_g

        df_green = pd.DataFrame(data_dict)
        df_green.to_excel(writer, sheet_name='Green', index=False)

    def _create_blue_sheet(self, writer):
        """创建Blue工作表"""
        data_dict = {
            'Position/Instrument': ['White borad Value', 'Calibration value', 'PWM']
        }

        # 添加各分类的B值列
        categories_order = ['白板', '校准', '皮肤1', '皮肤2', '皮肤3', '皮肤4', '皮肤5']
        for i, category in enumerate(categories_order, 1):
            data_dict[f'{i}_B'] = ['', '', '']

        # 填充B值数据
        for category in self.categories:
            if len(self.classified_data[category]) > 0:
                b_values = [entry['b_value'] for entry in self.classified_data[category] if entry['b_value'] is not None]
                avg_b = int(sum(b_values) / len(b_values)) if b_values else ''

                if category == '白板':
                    col_idx = 1
                elif category == '校准':
                    col_idx = 2
                elif category in ['皮肤1', '皮肤2', '皮肤3', '皮肤4', '皮肤5']:
                    col_idx = int(category[-1]) + 2
                else:
                    continue

                if category == '白板':
                    data_dict[f'{col_idx}_B'][0] = avg_b
                elif category == '校准':
                    data_dict[f'{col_idx}_B'][1] = avg_b

        # 添加皮肤数据行
        for category in ['皮肤1', '皮肤2', '皮肤3', '皮肤4', '皮肤5']:
            if category not in data_dict['Position/Instrument']:
                for key in data_dict.keys():
                    data_dict[key].append('')
                data_dict['Position/Instrument'][-1] = category

                # 填充该分类的B值
                if len(self.classified_data[category]) > 0:
                    b_values = [entry['b_value'] for entry in self.classified_data[category] if entry['b_value'] is not None]
                    avg_b = int(sum(b_values) / len(b_values)) if b_values else ''
                    col_idx = int(category[-1]) + 2
                    row_idx = len(data_dict['Position/Instrument']) - 1
                    data_dict[f'{col_idx}_B'][row_idx] = avg_b

        df_blue = pd.DataFrame(data_dict)
        df_blue.to_excel(writer, sheet_name='Blue', index=False)

    def _create_r_sheet(self, writer):
        """创建R工作表"""
        data_dict = {
            'Position/Instrument': ['White borad Value', 'Calibration value', 'PWM']
        }

        # 添加各分类的R值列 - 修正顺序：白板、校准、皮肤1-5
        categories_order = ['白板', '校准', '皮肤1', '皮肤2', '皮肤3', '皮肤4', '皮肤5']
        for i, category in enumerate(categories_order, 1):
            data_dict[f'{i}_R'] = ['', '', '']

        # 填充R值数据
        for category in self.categories:
            if len(self.classified_data[category]) > 0:
                r_values = [entry['r_value'] for entry in self.classified_data[category] if entry['r_value'] is not None]
                avg_r = round(sum(r_values) / len(r_values), 2) if r_values else ''

                if category == '白板':
                    col_idx = 1
                elif category == '校准':
                    col_idx = 2
                elif category in ['皮肤1', '皮肤2', '皮肤3', '皮肤4', '皮肤5']:
                    col_idx = int(category[-1]) + 2
                else:
                    continue

                if category == '白板':
                    data_dict[f'{col_idx}_R'][0] = avg_r
                elif category == '校准':
                    data_dict[f'{col_idx}_R'][1] = avg_r

        # 添加皮肤数据行
        for category in ['皮肤1', '皮肤2', '皮肤3', '皮肤4', '皮肤5']:
            if category not in data_dict['Position/Instrument']:
                for key in data_dict.keys():
                    data_dict[key].append('')
                data_dict['Position/Instrument'][-1] = category

                # 填充该分类的R值
                if len(self.classified_data[category]) > 0:
                    r_values = [entry['r_value'] for entry in self.classified_data[category] if entry['r_value'] is not None]
                    avg_r = round(sum(r_values) / len(r_values), 2) if r_values else ''
                    col_idx = int(category[-1]) + 2
                    row_idx = len(data_dict['Position/Instrument']) - 1
                    data_dict[f'{col_idx}_R'][row_idx] = avg_r

        df_r = pd.DataFrame(data_dict)
        df_r.to_excel(writer, sheet_name='R', index=False)

    def _create_summary_sheet(self, writer):
        """创建原始汇总表"""
        all_data = []
        for entry in self.raw_data:
            all_data.append({
                '文件名': entry['file'],
                '行号': entry['line_number'],
                '分类': entry['category'],
                '格式化数据': entry['formatted_gbr'],
                'G值': entry['g_value'],
                'B值': entry['b_value'],
                'R值': entry['r_value'],
                '原始数据': entry['original_line']
            })

        df_all = pd.DataFrame(all_data)
        df_all.to_excel(writer, sheet_name='原始数据汇总', index=False)
    
    def save_formatted_text(self, output_path):
        """保存格式化后的文本文件"""
        try:
            with open(output_path, 'w', encoding='utf-8') as f:
                f.write("数据分类和格式转换结果\n")
                f.write("="*50 + "\n\n")

                for category in self.categories:
                    if len(self.classified_data[category]) > 0:
                        f.write(f"{category}:\n")
                        for entry in self.classified_data[category]:
                            f.write(f"  {entry['formatted_gbr']}\n")
                        f.write("\n")

            print(f"格式化文本已保存到: {output_path}")

        except Exception as e:
            print(f"保存文本文件时出错: {str(e)}")




def main():
    """主函数"""
    # 创建数据分类器
    classifier = DataClassifier()

    # 要处理的文件
    files_to_process = ['10.txt', '20.txt']

    print("开始处理数据文件...")
    print("-" * 40)

    # 处理文件
    classifier.process_files(files_to_process)

    # 打印摘要
    classifier.print_summary()

    # 保存结果
    output_dir = "processed_results"
    if not os.path.exists(output_dir):
        os.makedirs(output_dir)

    # 保存到Excel
    excel_path = os.path.join(output_dir, "classified_data.xlsx")
    classifier.save_to_excel(excel_path)

    # 保存格式化文本
    text_path = os.path.join(output_dir, "formatted_data.txt")
    classifier.save_formatted_text(text_path)

    print(f"\n处理完成！结果保存在 {output_dir} 目录中")
    print("生成的文件:")
    print(f"  - Excel文件: {os.path.basename(excel_path)}")
    print(f"  - 文本文件: {os.path.basename(text_path)}")


def demo_run():
    """演示运行函数，显示处理过程"""
    print("="*60)
    print("数据分类和格式转换演示")
    print("="*60)
    print("功能说明:")
    print("1. 自动识别数据分类：校准、白板、皮肤1-5")
    print("2. 过滤指定命令：Got BLE CMD49、Got BLE CMD19")
    print("3. 格式转换：色板数据 → G:xxxx  B:xxxx  R:xxxx")
    print("4. 生成Excel报表和可视化图表")
    print("-" * 60)

    main()


if __name__ == "__main__":
    demo_run()
