import pandas as pd
import matplotlib.pyplot as plt

# 读取Excel文件

# T = '25℃'
T = 'room'
file_path = '2.6皮肤升温测试数据.xlsx'
df = pd.read_excel(file_path)

# 提取次数
times = df['次数']

# 定义仪器的列名（只有6个仪器的G值）
# instruments_G = ['仪器1_G', '仪器3_G', '仪器4_G']
# instruments_B = ['仪器1_B', '仪器3_B', '仪器4_B']
instruments_R = ['仪器1_R', '仪器6_R']

# plt.figure(figsize=(14, 8)) 
# # 绘制每个仪器的散点图G
# for i in [1,3,4]:
#     plt.scatter(times, df[f"仪器{i}_G"], label=f"instrument{i}")

# # 设置图例
# plt.legend()
# i = 'G'
# # 设置标题和轴标签
# plt.title(f'Relationship between Times and {i} Values')
# plt.xlabel('Times')
# plt.ylabel(f'{i} Values')
# # 显示网格
# plt.grid(True)
# # 显示图形
# plt.show()


# plt.figure(figsize=(14, 8)) 
# # 绘制每个仪器的散点图G
# for i in [1,3,4]:
#     plt.scatter(times, df[f"仪器{i}_B"], label=f"instrument{i}")

# # 设置图例
# plt.legend()
# i = 'B'
# # 设置标题和轴标签
# plt.title(f'Relationship between Times and {i} Values')
# plt.xlabel('Times')
# plt.ylabel(f'{i} Values')
# # 显示网格
# plt.grid(True)
# # 显示图形
# plt.show()

plt.figure(figsize=(14, 8)) 

# 绘制每个仪器的散点图R
for i in [1,6]:
    plt.scatter(times, df[f"仪器{i}_R"], label=f"instrument{i}")

# 设置图例
plt.legend()
i = 'R'
# 设置标题和轴标签
plt.title(f'Relationship between Times and {i} Values')
plt.xlabel('Times')
plt.ylabel(f'{i} Values')
# 显示网格
plt.grid(True)
# 显示图形
plt.show()

