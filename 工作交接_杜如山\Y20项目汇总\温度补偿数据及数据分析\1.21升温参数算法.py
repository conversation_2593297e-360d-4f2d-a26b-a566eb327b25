import pandas as pd
import matplotlib.pyplot as plt
import numpy as np

# 读取Excel文件
T = 'room'
file_path = '1.21升温测试2.xlsx'
df = pd.read_excel(file_path)

# 提取次数
times = df['次数']

# 定义仪器的列名
instruments_R = ['仪器1_R', '仪器2_R', '仪器3_R', '仪器4_R']

# 计算每个次数下所有仪器R的平均值
average_r_values = df.groupby('次数')[instruments_R].mean()
# 将平均值转换为一个DataFrame，其中每一行对应一个次数下的平均值
average_r_values_df = average_r_values.reset_index()
average_r_values_df['平均温度'] = average_r_values_df[instruments_R].mean(axis=1)

c = 0.07 # c=0.07的时候，符合现在算法的趋势

# 计算X的值并创建新的列Y
df['X'] = df['次数'].apply(lambda x: 
                           c * min(10, x) +
                           c *(0.5/0.7) * min(20-10, max(0, x-10)) +
                           c *(0.3/0.7) * min(30-20, max(0, x-20)) +
                           c *(0.1/0.7) * max(0, x-30) -1.9)

df['Y'] = df['仪器5_R'] + df['X']

# 绘制每个次数下所有仪器G值的平均值与次数的关系图
plt.figure(figsize=(14, 8))
plt.scatter(average_r_values_df['次数'], average_r_values_df['平均温度'], label='Average R ', color='blue')
plt.scatter(times, df['仪器5_R'], label='No compensate R ', color='Red')
plt.scatter(times, df['Y'], label='Compensated R ', color='Green')

plt.title(f'Average R Values by Times({T})')
plt.xlabel('Times')
plt.ylabel('R Value')
plt.legend()
plt.grid(True)
plt.show()