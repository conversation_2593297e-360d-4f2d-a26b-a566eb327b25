import pandas as pd
import matplotlib.pyplot as plt
import numpy as np

# 读取Excel文件

# T = '25℃'
T = 'room'
# file_path = '1.23初版算法手动.xlsx'
file_path = '1.21升温测试2.xlsx'
df = pd.read_excel(file_path)

# 提取次数
times = df['次数']

# 定义仪器的列名
# instruments_G = ['仪器1_G', '仪器2_G', '仪器3_G', '仪器4_G', '仪器5_G']
# instruments_B = ['仪器1_B', '仪器2_B', '仪器3_B', '仪器4_B', '仪器5_B']
# instruments_R = ['仪器1_R', '仪器2_R', '仪器3_R', '仪器4_R', '仪器5_R']
instruments_G = ['仪器4_G']
# instruments_B = ['仪器4_B']
# instruments_R = [ '仪器4_R']


plt.figure(figsize=(14, 8)) 
# 绘制每个仪器的散点图G
for i in range(1,6):
    plt.scatter(times, df[f"仪器{i}_G"], label=f"instrument{i}")
# plt.scatter(times, "仪器4_G", label=f"instrument4")

# 设置图例
plt.legend()
i = 'G'
# 设置标题和轴标签
plt.title(f'Relationship between Times and {i} Values')
plt.xlabel('Times')
plt.ylabel(f'{i} Values')
# 显示网格
plt.grid(True)
# 显示图形
plt.show()


plt.figure(figsize=(14, 8)) 
# 绘制每个仪器的散点图GR
# for i in range(1,6):
#     plt.scatter(times, df[f"仪器{i}_B"], label=f"instrument{i}")
plt.scatter(times, "仪器4_R", label=f"instrument4")
# 设置图例
plt.legend()
i = 'B'
# 设置标题和轴标签
plt.title(f'Relationship between Times and {i} Values')
plt.xlabel('Times')
plt.ylabel(f'{i} Values')
# 显示网格
plt.grid(True)
# 显示图形
plt.show()

plt.figure(figsize=(14, 8)) 
# 绘制每个仪器的散点图G
# for i in range(1,6):
#     plt.scatter(times, df[f"仪器{i}_R"], label=f"instrument{i}")
plt.scatter(times, "仪器4_R", label=f"instrument4")

# 设置图例
plt.legend()
i = 'R'
# 设置标题和轴标签
plt.title(f'Relationship between Times and {i} Values')
plt.xlabel('Times')
plt.ylabel(f'{i} Values')
# 显示网格
plt.grid(True)
# 显示图形
plt.show()



# 温度范围和对应的系数、截距
temperature_ranges = [(10, 15), (15, 20), (20, np.inf)]
coefficients = [-0.6486, -0.3412, -0.1452]
intercepts = [340.9907, 187.0115, 91.1400]

# 定义通过G值预测温度的函数
def predict_temperature(g_value):
    for i, temp_range in enumerate(temperature_ranges):
        predicted_temp = coefficients[i] * g_value + intercepts[i]
        if temp_range[0] <= predicted_temp <= temp_range[1]:
            return predicted_temp
    return np.nan  # 返回 NaN 作为无效值

# 初始化一个字典来存储每个仪器的温度
# instrument_temperatures = {f'仪器{i}_G': [] for i in range(1, 6)}
instrument_temperatures = '仪器4_G'
# 计算每个仪器的温度
for instrument in instrument_temperatures.keys():
    g_values = df[instrument]
    temperatures = [predict_temperature(g) for g in g_values]
    instrument_temperatures[instrument] = temperatures

# 绘制每个仪器的温度随次数变化的关系图
plt.figure(figsize=(14, 8))

for instrument, temperatures in instrument_temperatures.items():
    # 将 NaN 值替换为前一个有效值（或跳过）
    valid_temperatures = np.array(temperatures)
    valid_times = times[~np.isnan(valid_temperatures)]
    valid_temperatures = valid_temperatures[~np.isnan(valid_temperatures)]
    
    plt.scatter(valid_times, valid_temperatures, label=f'{instrument} Temperature')

# 添加图例
plt.legend()

# 添加标题和轴标签
plt.title('Relationship between Times and Instrument Temperatures')
plt.xlabel('Times')
plt.ylabel('Predicted Temperature (°C)')
plt.grid(True)
plt.show()