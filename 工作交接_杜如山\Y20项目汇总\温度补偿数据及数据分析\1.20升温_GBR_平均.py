import pandas as pd
import matplotlib.pyplot as plt
import numpy as np
from sklearn.linear_model import LinearRegression

# 读取Excel文件
T = 'room'
file_path = '1.20最大升温测试2.xlsx'
df = pd.read_excel(file_path)

# 提取次数
times = df['次数']

# 定义仪器的列名（只有6个仪器的G值）
instruments_G = ['仪器1_G', '仪器3_G', '仪器4_G']
instruments_B = ['仪器1_B', '仪器3_B', '仪器4_B']
instruments_R = ['仪器1_R', '仪器3_R', '仪器4_R']

# 计算每个次数下所有仪器G值的平均值
average_g_values = df.groupby('次数')[instruments_G].mean()
# 将平均值转换为一个DataFrame，其中每一行对应一个次数下的平均值
average_g_values_df = average_g_values.reset_index()
average_g_values_df['平均温度'] = average_g_values_df[instruments_G].mean(axis=1)
# 绘制每个次数下所有仪器G值的平均值与次数的关系图
plt.figure(figsize=(14, 8))
plt.scatter(average_g_values_df['次数'], average_g_values_df['平均温度'], label='Average G ', color='blue')
plt.title(f'Average G Values by Times({T})')
plt.xlabel('Times')
plt.ylabel('Average G Value')
plt.legend()
plt.grid(True)
plt.show()

# 计算每个次数下所有仪器B值的平均值
average_b_values = df.groupby('次数')[instruments_B].mean()
# 将平均值转换为一个DataFrame，其中每一行对应一个次数下的平均值
average_b_values_df = average_b_values.reset_index()
average_b_values_df['平均温度'] = average_b_values_df[instruments_B].mean(axis=1)
# 绘制每个次数下所有仪器G值的平均值与次数的关系图
plt.figure(figsize=(14, 8))
plt.scatter(average_b_values_df['次数'], average_b_values_df['平均温度'], label= 'Average B ', color='blue')
plt.title(f'Average B Values by Times({T})')
plt.xlabel('Times')
plt.ylabel('Average B Value')
plt.legend()
plt.grid(True)
plt.show()

# 计算每个次数下所有仪器R的平均值
average_r_values = df.groupby('次数')[instruments_R].mean()
# 将平均值转换为一个DataFrame，其中每一行对应一个次数下的平均值
average_r_values_df = average_r_values.reset_index()
average_r_values_df['平均温度'] = average_r_values_df[instruments_R].mean(axis=1)
# 绘制每个次数下所有仪器G值的平均值与次数的关系图
plt.figure(figsize=(14, 8))
plt.scatter(average_r_values_df['次数'], average_r_values_df['平均温度'], label='Average R ', color='blue')
plt.title(f'Average R Values by Times({T})')
plt.xlabel('Times')
plt.ylabel('Average R Value')
plt.legend()
plt.grid(True)
plt.show()


# 平均温度
temperature_ranges = [(10, 15), (15, 20), (20, np.inf)]
coefficients = [-0.6486, -0.3412, -0.1452]
intercepts = [340.9907, 187.0115, 91.1400]

# 定义通过G值预测温度的函数
def predict_temperature(g_value):
    for i, temp_range in enumerate(temperature_ranges):
        predicted_temp = coefficients[i] * g_value + intercepts[i]
        if temp_range[0] <= predicted_temp <= temp_range[1]:
            return predicted_temp
    return None

# 初始化一个列表来存储每个次数的平均温度
average_temperatures = []

# 计算每个次数的平均温度
for time in times.unique():
    time_index = times[times == time].index  # 找到当前次数的所有索引
    predicted_temps = []
    
    for instrument in ['仪器1', '仪器3', '仪器4']:
        g_values = df[f'{instrument}_G'].iloc[time_index]
        for g in g_values:
            predicted_temp = predict_temperature(g)
            if predicted_temp is not None:
                predicted_temps.append(predicted_temp)
    
    if predicted_temps:
        average_temperature = np.mean(predicted_temps)
        average_temperatures.append((time, average_temperature))

# 将结果转换为DataFrame
average_temperatures_df = pd.DataFrame(average_temperatures, columns=['次数', '平均温度'])

# 绘制次数与平均温度的关系图
plt.figure(figsize=(14, 8))
plt.scatter(average_temperatures_df['次数'], average_temperatures_df['平均温度'], color='blue', label='Average Predicted Temperature')


# 添加图例
plt.legend()

# 添加标题和轴标签
plt.title('Relationship between Times and Average Temperatures')
plt.xlabel('Times')
plt.ylabel('Average Predicted Temperature (°C)')
plt.grid(True)
plt.show()

# 添加图例
plt.legend()