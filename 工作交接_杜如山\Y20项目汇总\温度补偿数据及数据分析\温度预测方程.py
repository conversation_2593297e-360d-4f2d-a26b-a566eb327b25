import pandas as pd
import matplotlib.pyplot as plt
from sklearn.linear_model import LinearRegression
import numpy as np

# 读取Excel文件
# df = pd.read_excel('1.10温度梯度重置表格.xlsx')
df = pd.read_excel('1.10温度梯度重置表格.xlsx')

# 提取温度、6个仪器的G值数据
temperatures = df['温度']
g_values = df[['仪器1_G', '仪器2_G', '仪器3_G', '仪器4_G', '仪器5_G', '仪器6_G']]

# 创建一个新的DataFrame
data = pd.DataFrame({
    'Temperature': temperatures,
    'G_仪器1_G': g_values['仪器1_G'],
    'G_仪器2_G': g_values['仪器2_G'],
    'G_仪器3_G': g_values['仪器3_G'],
    'G_仪器4_G': g_values['仪器4_G'],
    'G_仪器5_G': g_values['仪器5_G'],
    'G_仪器6_G': g_values['仪器6_G'],
})

# 计算每个温度点的平均G值
data['Average_G'] = data[['G_仪器1_G', 'G_仪器2_G', 'G_仪器3_G', 'G_仪器4_G', 'G_仪器5_G', 'G_仪器6_G']].mean(axis=1)

# 定义温度区间
temperature_ranges = [(10, 15), (15, 20), (20, np.inf)]

# 创建图形和轴
plt.figure(figsize=(12, 8))

# 对每个温度区间进行线性回归拟合并绘制散点图和回归曲线
for i, temp_range in enumerate(temperature_ranges):
    # 筛选出当前温度区间的数据
    if temp_range[1] == np.inf:
        filtered_data = data[data['Temperature'] >= temp_range[0]]
    else:
        filtered_data = data[(data['Temperature'] >= temp_range[0]) & (data['Temperature'] <= temp_range[1])]
    
    # 检查筛选后的数据集是否为空
    if filtered_data.empty:
        print(f"温度区间 {temp_range} 的数据为空，跳过该区间。")
        continue
    
    # 准备线性回归模型的数据
    X = filtered_data[['Average_G']]
    y = filtered_data['Temperature']
    
    # 创建线性回归模型
    model = LinearRegression()
    model.fit(X, y)
    
    # 获取系数和截距
    coef = model.coef_[0]
    intercept = model.intercept_
    
    # 输出线性回归系数和截距
    print(f"温度区间 {temp_range} 的线性回归系数: {coef:.4f}, 截距: {intercept:.4f}")
    
    # 绘制散点图
    plt.scatter(filtered_data['Average_G'], filtered_data['Temperature'], label=f'Temperature Range {temp_range}')
    
    # 绘制回归曲线
    g_range = np.linspace(filtered_data['Average_G'].min(), filtered_data['Average_G'].max(), 100)
    plt.plot(g_range, model.predict(np.array([g_range]).T), label=f'Regression Line {temp_range}', linestyle='--')

# 添加标题和标签
plt.title('Linear Regression of Temperature vs Average G Values by Temperature Range')
plt.xlabel('Average G Value')
plt.ylabel('Temperature (°C)')
plt.legend()
plt.grid(True)
plt.show()

#温度预测方程

import numpy as np


# 定义温度区间的线性回归系数和截距 第一次
temperature_ranges = [(10, 15), (15, 20), (20, np.inf)]
coefficients = [-0.6486, -0.3412, -0.1452]
intercepts = [340.9907, 187.0115, 91.1400]
# 定义温度区间的线性回归系数和截距 第二次
temperature_ranges = [(10, 15), (15, 20), (20, np.inf)]
coefficients = [-0.3449, -0.1508, -0.1469]
intercepts = [185.5723, 89.6286, 86.9757]

def predict_temperature(g_value):
    # 遍历每个温度区间
    for i, temp_range in enumerate(temperature_ranges):
        # 计算预测温度
        predicted_temp = coefficients[i] * g_value + intercepts[i]
        # 检查预测温度是否在当前区间内
        if temp_range[0] <= predicted_temp <= temp_range[1]:
            return predicted_temp
    # 如果没有合适的区间，返回None
    return None