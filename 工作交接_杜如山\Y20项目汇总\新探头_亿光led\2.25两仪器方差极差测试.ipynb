{"cells": [{"cell_type": "code", "execution_count": 16, "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 800x500 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["各位置R值方差对比：\n", "   位置  仪器A_R_var  仪器4_R_var\n", "0   1     0.0556     0.0284\n", "1   2     0.1567     0.0812\n", "2   3     0.0384     0.0081\n", "3   4     0.0328     0.0371\n"]}, {"data": {"image/png": "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", "text/plain": ["<Figure size 1000x600 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["import pandas as pd\n", "import os\n", "import matplotlib.pyplot as plt\n", "import numpy as np\n", "import matplotlib.pyplot as plt\n", "\n", "# 设置全局字体（需确保系统中存在该字体）\n", "plt.rcParams['font.sans-serif'] = ['Microsoft YaHei']  # 微软雅黑，Windows自带\n", "# 或使用其他字体如 ['Sim<PERSON><PERSON>', '<PERSON><PERSON><PERSON>', 'Song<PERSON> SC']（根据系统实际字体名调整）\n", "plt.rcParams['axes.unicode_minus'] = False  # 解决负号显示问题\n", "\n", "\n", "os.chdir(\"D:\\E3A\\亿杉算法实习\\温度补偿\\新探头_亿光led\")\n", "# 读取数据\n", "\n", "# 重新运行绘图代码\n", "\n", "# ================== 第一部分：数据加载与方差计算 ==================\n", "# 读取Excel文件（假设数据已加载）\n", "file_path = '2.25新探头对比测试.xlsx'\n", "df = pd.read_excel(file_path)\n", "\n", "def calculate_stats(group):\n", "    \"\"\"计算每个位置的所有统计指标\"\"\"\n", "    stats = {}\n", "    for instrument in ['仪器A', '仪器4']:\n", "        for channel in ['G', 'B', 'R']:  # 按G、B、R顺序计算\n", "            col = f\"{instrument}_{channel}\"\n", "            # 计算均值和极差\n", "            stats[f\"{col}_mean\"] = group[col].mean()\n", "            stats[f\"{col}_range\"] = group[col].max() - group[col].min()\n", "    return pd.Series(stats)\n", "\n", "# 按位置分组计算统计量\n", "result = df.groupby('位置').apply(calculate_stats).reset_index()\n", "\n", "# ================== 生成独立表格 ==================\n", "# 定义列顺序：位置 + 仪器A_G_mean, 仪器A_B_mean, 仪器A_R_mean, 仪器4_G_mean, ...\n", "column_order = ['位置'] + [\n", "    f\"{instrument}_{channel}_{stat}\"\n", "    for instrument in ['仪器A', '仪器4']\n", "    for channel in ['G', 'B', 'R']\n", "    for stat in ['mean', 'range']\n", "]\n", "\n", "# 提取平均值表格（仅包含_mean列）\n", "mean_df = result[[col for col in result.columns if '_mean' in col]]\n", "mean_df.insert(0, '位置', result['位置'])  # 确保位置列为第一列\n", "\n", "# 提取极差表格（仅包含_range列）\n", "range_df = result[[col for col in result.columns if '_range' in col]]\n", "range_df.insert(0, '位置', result['位置'])  # 确保位置列为第一列\n", "\n", "# 设置保留两位小数\n", "mean_df = mean_df.round(2)\n", "range_df = range_df.round(2)\n", "\n", "# # ================== 打印结果 ==================\n", "# print(\"============= 平均值表格 =============\")\n", "# print(mean_df)\n", "# print(\"\\n============= 极差表格 ===============\")\n", "# print(range_df)\n", "\n", "r_range_data = range_df[['位置', '仪器A_R_range', '仪器4_R_range']]\n", "\n", "# 绘制R值极差对比图\n", "plt.figure(figsize=(8, 5))\n", "positions = r_range_data['位置']\n", "plt.plot(positions, r_range_data['仪器A_R_range'], marker='o', label='仪器A')\n", "plt.plot(positions, r_range_data['仪器4_R_range'], marker='s', label='仪器4')\n", "plt.xlabel('位置')\n", "plt.ylabel('极差')\n", "plt.title('R值极差对比（仪器A vs 仪器4）')\n", "plt.xticks(positions)\n", "plt.legend()\n", "plt.grid(True)\n", "plt.tight_layout()\n", "plt.show()\n", "\n", "# 定义统计函数\n", "def calculate_var(group):\n", "    stats = {}\n", "    # 仪器A和仪器4的方差计算\n", "    for instrument in ['仪器A', '仪器4']:\n", "        for channel in ['R', 'G', 'B']:\n", "            col = f\"{instrument}_{channel}\"\n", "            stats[f\"{col}_var\"] = group[col].var()\n", "    return pd.Series(stats)\n", "\n", "# 按位置分组计算方差\n", "var_result = df.groupby('位置').apply(calculate_var).reset_index()\n", "\n", "# 提取R通道方差\n", "r_vars = var_result[['位置', '仪器A_R_var', '仪器4_R_var']]\n", "\n", "# 打印方差结果\n", "print(\"各位置R值方差对比：\")\n", "print(r_vars.round(4))\n", "\n", "# ================== 第二部分：可视化 ==================\n", "# 配置绘图参数\n", "plt.figure(figsize=(10, 6))\n", "bar_width = 0.35\n", "positions = np.arange(len(r_vars['位置']))\n", "\n", "# 绘制柱状图\n", "plt.bar(positions - bar_width/2, r_vars['仪器A_R_var'], width=bar_width, label='仪器A')\n", "plt.bar(positions + bar_width/2, r_vars['仪器4_R_var'], width=bar_width, label='仪器4')\n", "\n", "# 添加标签和标题\n", "plt.xticks(positions, r_vars['位置'])\n", "plt.xlabel('位置')\n", "plt.ylabel('方差')\n", "plt.title('不同位置R值方差对比')\n", "plt.legend()\n", "\n", "plt.tight_layout()\n", "plt.show()\n", "\n", "\n", "\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["============= 平均值表格 =============\n", "   位置  仪器A_G_mean  仪器A_B_mean  仪器A_R_mean  仪器4_G_mean  仪器4_B_mean  仪器4_R_mean\n", "0   1       396.7       757.9        2.20       806.2       697.7        3.53\n", "1   2       327.1       539.7        7.44       680.6       530.6        6.92\n", "2   3       298.1       517.1        7.56       632.6       510.6        7.29\n", "3   4       308.5       544.4        7.24       659.1       525.1        7.00\n", "\n", "============= 方差表格 ===============\n", "   位置  仪器A_G_var  仪器A_B_var  仪器A_R_var  仪器4_G_var  仪器4_B_var  仪器4_R_var\n", "0   1     100.46     208.10       0.06     109.07      84.23       0.03\n", "1   2     218.10     387.12       0.16    1299.16     226.93       0.08\n", "2   3      44.32      55.88       0.04     366.49      27.38       0.01\n", "3   4      45.39      66.04       0.03     630.99     100.99       0.04\n", "\n", "结果已保存到：分表格统计结果.xlsx\n"]}], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.11"}}, "nbformat": 4, "nbformat_minor": 2}