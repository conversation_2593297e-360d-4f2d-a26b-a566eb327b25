import pandas as pd
import matplotlib.pyplot as plt

# 读取Excel文件
i = 'R'
# T = '25℃'
T = 'room'
file_path = '1.16最大升温-室温.xlsx'
df = pd.read_excel(file_path)

# 提取次数
times = df['次数']

# 定义仪器的列名（只有6个仪器的G值）
# instruments = ['仪器1_G', '仪器2_G', '仪器3_G', '仪器4_G', '仪器5_G', '仪器6_G']
#instruments = ['仪器1_B', '仪器2_B', '仪器3_B', '仪器4_B', '仪器5_B', '仪器6_B']
instruments = ['仪器1_R', '仪器2_R', '仪器3_R', '仪器4_R', '仪器5_R']

# 计算每个次数下所有仪器G值的平均值
average_g_values = df.groupby('次数')[instruments].mean()


# 将平均值转换为一个DataFrame，其中每一行对应一个次数下的平均值
average_g_values_df = average_g_values.reset_index()
average_g_values_df['平均温度'] = average_g_values_df[instruments].mean(axis=1)



# 绘制每个次数下所有仪器G值的平均值与次数的关系图
plt.figure(figsize=(14, 8))
plt.scatter(average_g_values_df['次数'], average_g_values_df['平均温度'], label=f'Average{i} ', color='blue')
plt.title(f'Average {i} Values by Times({T})')
plt.xlabel('Times')
plt.ylabel(f'Average {i} Value')
plt.legend()
plt.grid(True)
plt.show()