皮肤数据折线图生成器使用说明

功能描述：
=========
从result.xlsx文件读取皮肤数据，为5种皮肤生成独立的GBR折线图

使用方法：
=========
1. 确保已运行 process_10_20_files.py 生成 result.xlsx 文件
2. 运行命令：python generate_skin_charts.py
3. 图表将保存在 charts_output 目录中

输出文件：
=========
- 皮肤1_GBR折线图.png
- 皮肤2_GBR折线图.png  
- 皮肤3_GBR折线图.png
- 皮肤4_GBR折线图.png
- 皮肤5_GBR折线图.png

图表特点：
=========
📊 横坐标：测试次数（1-10次）
📈 纵坐标：数值大小
🎨 颜色区分：
   - G-10：红色实线，圆点标记
   - B-10：蓝色实线，方点标记
   - R-10：绿色实线，三角标记
   - G-20：橙色虚线，菱形标记
   - B-20：紫色虚线，叉号标记
   - R-20：品红虚线，星号标记

📋 图表信息：
   - 标题：显示对应皮肤类型
   - 网格：浅色网格便于读数
   - 图例：显示各条线的含义
   - 高分辨率：300 DPI，适合打印

数据来源：
=========
- 不包含白板、校准和PWM数据
- 只显示5种皮肤的GBR测量值
- 10.txt和20.txt的数据分别显示
- 每种皮肤最多10次测量数据

依赖库：
=======
- matplotlib：图表绘制
- openpyxl：Excel文件读取
- numpy：数值计算

安装依赖：
=========
pip install matplotlib openpyxl numpy

数据分析：
=========
从生成的图表可以观察到：
1. 10.txt和20.txt数据的差异
2. G、B、R值的变化趋势
3. 不同皮肤类型的数值特征
4. 测量数据的稳定性和波动情况

注意事项：
=========
- 确保result.xlsx文件存在且格式正确
- 图表文件为PNG格式，高质量输出
- 如果某个数据系列为空，该线条不会显示
- 中文字体已配置，支持中文标题和标签
