{"cells": [{"cell_type": "code", "execution_count": 14, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["当前工作目录: d:\\E3A\\亿杉算法实习\\温度补偿\\新探头_亿光led\n", "新仪器组 G 值的方差: 1835.5900000000006\n", "旧仪器组 G 值的方差: 43.6275\n", "新仪器组 B 值的方差: 36795.9475\n", "旧仪器组 B 值的方差: 41.75\n"]}, {"data": {"image/png": "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", "text/plain": ["<Figure size 1000x600 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"image/png": "iVBORw0KGgoAAAANSUhEUgAAA0sAAAIfCAYAAAChLEcjAAAAOXRFWHRTb2Z0d2FyZQBNYXRwbG90bGliIHZlcnNpb24zLjUuMiwgaHR0cHM6Ly9tYXRwbG90bGliLm9yZy8qNh9FAAAACXBIWXMAAA9hAAAPYQGoP6dpAABY50lEQVR4nO3deVxU9f7H8few7yDihqAgYZlLZlFcC5dSKde0Tc1c+pl6zWylm5W5dNO00qxr9+aSl<PERSON>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", "text/plain": ["<Figure size 1000x600 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["#对空\n", "import pandas as pd\n", "import matplotlib.pyplot as plt\n", "import os\n", "from sklearn.metrics import mean_squared_error\n", "\n", "# 打印当前工作目录\n", "print(\"当前工作目录:\", os.getcwd())\n", "\n", "# 如果需要，更改工作目录\n", "os.chdir(\"D:\\E3A\\亿杉算法实习\\温度补偿\\新探头_亿光led\")#有数字需要双反斜杠\n", "# 设置全局字体为 SimHei（黑体）\n", "plt.rcParams['font.sans-serif'] = ['SimHei']  # 也可以选择其他中文字体\n", "plt.rcParams['axes.unicode_minus'] = False  # 正确显示负号\n", "\n", "# 读取Excel文件\n", "file_path = '2.10暗环境测试 .xlsx'\n", "df = pd.read_excel(file_path)\n", "\n", "# 定义仪器的列名\n", "instruments_G = ['仪器1_G', '仪器2_G', '仪器3_G', '仪器4_G', '仪器6_G', '仪器7_G', '仪器9_G', '仪器10_G']\n", "instruments_B = ['仪器1_B', '仪器2_B', '仪器3_B', '仪器4_B', '仪器6_B', '仪器7_B', '仪器9_B', '仪器10_B']\n", "\n", "# 定义新仪器和旧仪器的分组\n", "new_instruments = ['仪器1_G', '仪器2_G', '仪器3_G', '仪器4_G']\n", "old_instruments = ['仪器6_G', '仪器7_G', '仪器9_G', '仪器10_G']\n", "\n", "# 提取G值和B值\n", "new_instruments_G = df[new_instruments].values.flatten()\n", "old_instruments_G = df[old_instruments].values.flatten()\n", "new_instruments_B = df[[f'仪器{i}_B' for i in [1, 2, 3, 4]]].values.flatten()\n", "old_instruments_B = df[[f'仪器{i}_B' for i in [6, 7, 9, 10]]].values.flatten()\n", "\n", "# 创建仪器标签\n", "new_instruments_labels = ['1', '2', '3', '4'] * 5  # 每个仪器5次\n", "old_instruments_labels = ['6', '7', '9', '10'] * 5  # 每个仪器5次\n", "\n", "var_new_G = new_instruments_G.var(axis=0)  # 沿着列的方向计算方差\n", "var_old_G = old_instruments_G.var(axis=0)  # 沿着列的方向计算方差\n", "var_new_B = new_instruments_B.var(axis=0)  # 沿着列的方向计算方差\n", "var_old_B = old_instruments_B.var(axis=0)  # 沿着列的方向计算方差\n", "\n", "# 打印方差\n", "print(f\"新仪器组 G 值的方差: {var_new_G}\")\n", "print(f\"旧仪器组 G 值的方差: {var_old_G}\")\n", "print(f\"新仪器组 B 值的方差: {var_new_B}\")\n", "print(f\"旧仪器组 B 值的方差: {var_old_B}\")\n", "\n", "# 绘制G值的散点图\n", "plt.figure(figsize=(10, 6))\n", "plt.scatter(new_instruments_labels, new_instruments_G, label='New Instruments (G)', color='blue')\n", "plt.scatter(old_instruments_labels, old_instruments_G, label='Old Instruments (G)', color='red')\n", "plt.legend()\n", "plt.title('Scatt<PERSON> Plot of G Values-对空')\n", "plt.xlabel('Instrument')\n", "plt.ylabel('G Values')\n", "plt.grid(True)\n", "plt.show()\n", "\n", "# 绘制B值的散点图\n", "plt.figure(figsize=(10, 6))\n", "plt.scatter(new_instruments_labels, new_instruments_B, label='New Instruments (B)', color='blue')\n", "plt.scatter(old_instruments_labels, old_instruments_B, label='Old Instruments (B)', color='red')\n", "plt.legend()\n", "plt.title('Scatter Plot of B Values-对空')\n", "plt.xlabel('Instrument')\n", "plt.ylabel('B Values')\n", "plt.grid(True)\n", "plt.show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["2.11 20mg-004测试"]}, {"cell_type": "code", "execution_count": 15, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["当前工作目录: d:\\E3A\\亿杉算法实习\\温度补偿\\新探头_亿光led\n", "新仪器组 G 值的方差: 29425.589999999997\n", "旧仪器组 G 值的方差: 854.2275\n", "新仪器组 B 值的方差: 156525.44\n", "旧仪器组 B 值的方差: 144.8275\n"]}, {"data": {"image/png": "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", "text/plain": ["<Figure size 1000x600 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"image/png": "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", "text/plain": ["<Figure size 1000x600 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["#20mg色卡\n", "import pandas as pd\n", "import matplotlib.pyplot as plt\n", "import os\n", "from sklearn.metrics import mean_squared_error\n", "\n", "# 打印当前工作目录\n", "print(\"当前工作目录:\", os.getcwd())\n", "\n", "# 如果需要，更改工作目录\n", "os.chdir(\"D:\\E3A\\亿杉算法实习\\温度补偿\\新探头_亿光led\")#有数字需要双反斜杠\n", "# 设置全局字体为 SimHei（黑体）\n", "plt.rcParams['font.sans-serif'] = ['SimHei']  # 也可以选择其他中文字体\n", "plt.rcParams['axes.unicode_minus'] = False  # 正确显示负号\n", "\n", "# 读取Excel文件\n", "file_path = '2.11_20mg-004色卡测试.xlsx'\n", "df = pd.read_excel(file_path)\n", "\n", "# 定义仪器的列名\n", "instruments_G = ['仪器1_G', '仪器2_G', '仪器3_G', '仪器4_G', '仪器6_G', '仪器7_G', '仪器9_G', '仪器10_G']\n", "instruments_B = ['仪器1_B', '仪器2_B', '仪器3_B', '仪器4_B', '仪器6_B', '仪器7_B', '仪器9_B', '仪器10_B']\n", "\n", "# 定义新仪器和旧仪器的分组\n", "new_instruments = ['仪器1_G', '仪器2_G', '仪器3_G', '仪器4_G']\n", "old_instruments = ['仪器6_G', '仪器7_G', '仪器9_G', '仪器10_G']\n", "\n", "# 提取G值和B值\n", "new_instruments_G = df[new_instruments].values.flatten()\n", "old_instruments_G = df[old_instruments].values.flatten()\n", "new_instruments_B = df[[f'仪器{i}_B' for i in [1, 2, 3, 4]]].values.flatten()\n", "old_instruments_B = df[[f'仪器{i}_B' for i in [6, 7, 9, 10]]].values.flatten()\n", "\n", "# 创建仪器标签\n", "new_instruments_labels = ['1', '2', '3', '4'] * 5  # 每个仪器5次\n", "old_instruments_labels = ['6', '7', '9', '10'] * 5  # 每个仪器5次\n", "\n", "var_new_G = new_instruments_G.var(axis=0)  # 沿着列的方向计算方差\n", "var_old_G = old_instruments_G.var(axis=0)  # 沿着列的方向计算方差\n", "var_new_B = new_instruments_B.var(axis=0)  # 沿着列的方向计算方差\n", "var_old_B = old_instruments_B.var(axis=0)  # 沿着列的方向计算方差\n", "\n", "# 打印方差\n", "print(f\"新仪器组 G 值的方差: {var_new_G}\")\n", "print(f\"旧仪器组 G 值的方差: {var_old_G}\")\n", "print(f\"新仪器组 B 值的方差: {var_new_B}\")\n", "print(f\"旧仪器组 B 值的方差: {var_old_B}\")\n", "\n", "# 绘制G值的散点图\n", "plt.figure(figsize=(10, 6))\n", "plt.scatter(new_instruments_labels, new_instruments_G, label='New Instruments (G)', color='blue')\n", "plt.scatter(old_instruments_labels, old_instruments_G, label='Old Instruments (G)', color='red')\n", "plt.legend()\n", "plt.title('Scatter Plot of G Values-20mg色卡')\n", "plt.xlabel('Instrument')\n", "plt.ylabel('G Values')\n", "plt.grid(True)\n", "plt.show()\n", "\n", "# 绘制B值的散点图\n", "plt.figure(figsize=(10, 6))\n", "plt.scatter(new_instruments_labels, new_instruments_B, label='New Instruments (B)', color='blue')\n", "plt.scatter(old_instruments_labels, old_instruments_B, label='Old Instruments (B)', color='red')\n", "plt.legend()\n", "plt.title('Scatter Plot of B Values-20mg色卡')\n", "plt.xlabel('Instrument')\n", "plt.ylabel('B Values')\n", "plt.grid(True)\n", "plt.show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["这个是测试新仪器和老仪器波动性的（2.17）"]}, {"cell_type": "code", "execution_count": 28, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["0      99.100913\n", "1      99.758454\n", "2     101.731079\n", "3     102.294686\n", "4     101.918948\n", "5     103.985507\n", "6     100.603865\n", "7     101.637144\n", "8     102.012882\n", "9      98.161567\n", "10     98.255502\n", "11     97.128288\n", "12    102.200751\n", "13     97.785829\n", "14     97.879764\n", "15     95.343532\n", "16     97.879764\n", "17    100.228127\n", "18    102.106817\n", "19     99.476651\n", "20    100.509930\n", "Name: 仪器1_G, dtype: float64\n", "0     108.160088\n", "1     106.401387\n", "2     103.637715\n", "3     106.778252\n", "4     103.512093\n", "         ...    \n", "77    100.371556\n", "78    100.120314\n", "79     99.240963\n", "80     97.607884\n", "81     96.477291\n", "Name: 仪器2_G, Length: 71, dtype: float64\n", "0     101.909322\n", "1     102.466966\n", "2     104.558128\n", "3     102.885198\n", "4     102.885198\n", "5     102.048733\n", "6     100.794036\n", "7      99.539338\n", "8      97.169354\n", "9      99.260516\n", "10     98.284641\n", "11    100.375803\n", "12    103.024609\n", "13     98.284641\n", "14     98.981695\n", "15     99.539338\n", "16    100.096981\n", "17     98.842284\n", "18     96.472300\n", "19     99.957571\n", "20     98.981695\n", "21     97.448176\n", "22     96.193478\n", "Name: 仪器3_G, dtype: float64\n", "0     101.052845\n", "1     101.903219\n", "2     102.611865\n", "3     104.029156\n", "4      99.635554\n", "5     100.060741\n", "6     100.485928\n", "7     100.911116\n", "8     100.485928\n", "9      99.210366\n", "10     97.793076\n", "11     99.777283\n", "12    100.060741\n", "13    101.194574\n", "14     99.210366\n", "15     99.635554\n", "16     99.777283\n", "17    100.627657\n", "18     97.651346\n", "19     96.517514\n", "20     97.367888\n", "Name: 仪器4_G, dtype: float64\n", "0     108.058824\n", "1     104.500000\n", "2     106.117647\n", "3     102.558824\n", "4     104.176471\n", "5      98.029412\n", "6      94.147059\n", "7      96.411765\n", "8     100.294118\n", "9      99.000000\n", "10    102.882353\n", "11     98.676471\n", "12     98.352941\n", "13     96.735294\n", "14     93.500000\n", "15    101.588235\n", "16     99.647059\n", "17     99.323529\n", "18    100.617647\n", "19     96.411765\n", "20     98.676471\n", "21    100.294118\n", "Name: 仪器6_G, dtype: float64\n", "0     106.506226\n", "1     106.506226\n", "2     103.120190\n", "3     102.812369\n", "4     100.041976\n", "5     101.273262\n", "6      98.502868\n", "7      98.502868\n", "8      99.118511\n", "9      98.195047\n", "10    100.349797\n", "11     99.734154\n", "12     97.271582\n", "13     99.118511\n", "14    101.888904\n", "15    103.735833\n", "16     94.809011\n", "17     97.579404\n", "18     97.887225\n", "19     97.271582\n", "20    101.888904\n", "21     93.885546\n", "Name: 仪器9_G, dtype: float64\n", "0     104.023904\n", "1     105.418327\n", "2     106.254980\n", "3     101.235060\n", "4      98.725100\n", "5      97.609562\n", "6      97.330677\n", "7      99.840637\n", "8     100.398406\n", "9     100.956175\n", "10    102.350598\n", "11     98.446215\n", "12     97.888446\n", "13     97.609562\n", "14     98.446215\n", "15     97.888446\n", "16     97.051793\n", "17     99.282869\n", "18     97.609562\n", "19    103.466135\n", "20     98.167331\n", "Name: 仪器10_G, dtype: float64\n"]}, {"data": {"image/png": "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", "text/plain": ["<Figure size 1200x800 with 2 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["import pandas as pd\n", "import numpy as np\n", "import matplotlib.pyplot as plt\n", "\n", "# 配置参数\n", "new_instruments = ['1', '2', '3', '4']\n", "old_instruments = ['6', '9', '10']\n", "colors = {'new':'#1f77b4', 'old':'#ff7f0e'}  # 蓝为新仪器，橙为老仪器\n", "\n", "# 读取数据\n", "df = pd.read_excel('2.17固定点测试.xlsx')\n", "\n", "# 数据存储字典\n", "variances = {\n", "    'G': {'new': [], 'old': []},\n", "    'B': {'new': [], 'old': []}\n", "}\n", "\n", "def process_instruments(instrument_type, instruments):\n", "    \"\"\"处理指定类型仪器的数据\"\"\"\n", "    for inst in instruments:\n", "        # 提取数据\n", "        g_data = df[f'仪器{inst}_G'].dropna()\n", "        b_data = df[f'仪器{inst}_B'].dropna()\n", "        \n", "        # 归一化处理\n", "        g_norm = g_data / g_data.mean() *100\n", "        b_norm = b_data / b_data.mean() *100\n", "        \n", "        print(g_norm)\n", "        \n", "        # 存储方差\n", "        variances['G'][instrument_type].append(g_norm.var())\n", "        variances['B'][instrument_type].append(b_norm.var())\n", "\n", "# 处理新旧仪器数据\n", "process_instruments('new', new_instruments)\n", "process_instruments('old', old_instruments)\n", "\n", "# 可视化设置\n", "plt.figure(figsize=(12, 8))\n", "\n", "# G值方差对比\n", "plt.subplot(2, 1, 1)\n", "x_pos = np.arange(len(new_instruments + old_instruments))\n", "plt.bar(x_pos[:4], variances['G']['new'], width=0.4, color=colors['new'], label='新仪器')\n", "plt.bar(x_pos[4:]-0.4, variances['G']['old'], width=0.4, color=colors['old'], label='老仪器')\n", "plt.xticks(x_pos, new_instruments + old_instruments)\n", "plt.title('G值归一化方差对比（新仪器 vs 老仪器）')\n", "plt.ylabel('方差')\n", "plt.legend()\n", "plt.grid(True, alpha=0.3)\n", "\n", "# B值方差对比\n", "plt.subplot(2, 1, 2)\n", "plt.bar(x_pos[:4], variances['B']['new'], width=0.4, color=colors['new'], label='新仪器')\n", "plt.bar(x_pos[4:]-0.4, variances['B']['old'], width=0.4, color=colors['old'], label='老仪器')\n", "plt.xticks(x_pos, new_instruments + old_instruments)\n", "plt.title('B值归一化方差对比（新仪器 vs 老仪器）')\n", "plt.ylabel('方差')\n", "plt.legend()\n", "plt.grid(True, alpha=0.3)\n", "\n", "plt.tight_layout()\n", "plt.show()"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.11"}}, "nbformat": 4, "nbformat_minor": 2}